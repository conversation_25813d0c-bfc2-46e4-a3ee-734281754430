[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[] NAME:增加新功能，工程文件快照管理器 DESCRIPTION: 工程文件快照管理器实现以下功能，为工程内的文件创建快照版本、获取已建立快照的文件列表等；同一个文件允许多版本；请为该功能自行设计存储方案，建议存储在用户目录下 `.yike` 目录中。该工程文件快照管理器后续将为智能体提供快照功能，注意模块代码组织。 注意：该管理器要与智能体会话进行绑定（当前智能体使用的是lang-graph， threadId 即会话ID），不同会话采用不同管理器，因此管理器在设计存储结构时，注意隔离。建议采用工程模式对管理器进行按需创建。
-[] NAME:增加新功能，ai-chat文件变更管理器 DESCRIPTION:在webui目录，ai-chat功能中，对话输入框的上方，绘制文件变更管理器新组件，该组件收集了智能体产生的文件变更（编程智能体会借助工具对文件进行修改）。在该组件中，每个变更文件条目都提供了相同的操作：`接受`、`撤销`。接受即表示采纳AI的文件变更，撤销即表示撤销AI的文件变更。该组件要借助 ‘工程文件快照管理器’提供的文件快照能力。变更管理策略：智能体借助工具对工程文件直接修改，改动前存储快照，多次对同一文件修改，快照仅存最老版本。当文件所有改动`接受`、`撤销`后，快照版本删除，文件变更管理器移除文件。文件变更管理器中的文件条目可被点击，点击后打开vscode的文件对比编辑器；注意`接受`按钮实际上并不对工程文件进行变更，实际变动在该策略下已经发生。注意快照创建的时机，需要到相关的工具调用代码中进行触发。