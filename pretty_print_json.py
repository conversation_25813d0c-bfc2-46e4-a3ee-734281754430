#!/usr/bin/env python3
import json
import os

def read_and_format_json(file_path):
    """
    Read a JSON file and return its formatted string representation.
    
    Args:
        file_path (str): Path to the JSON file
        
    Returns:
        str: Formatted JSON string
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

        data = data[1]["agent"]["messages"][0]["kwargs"]["additional_kwargs"]["tool_calls"][0]["function"]
        # arguments = json.load(data["arguments"])
        print(data["arguments"])
        print(" - - - - - -  - - - - - -  - - - - - -  - - - - - -  - - - - - -  - - - - - - ")
        return json.dumps(data, indent=2, ensure_ascii=False)
    except json.JSONDecodeError as e:
        return f"Error decoding JSON: {str(e)}"
    except Exception as e:
        return f"Error reading file: {str(e)}"

if __name__ == "__main__":
    # Get the directory of the current script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # Path to the failed.json file in the same directory
    failed_json_path = os.path.join(script_dir, 'failed.json')
    
    # Print the formatted JSON
    print(read_and_format_json(failed_json_path))
