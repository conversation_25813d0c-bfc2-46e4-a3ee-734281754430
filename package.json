{"name": "yike", "displayName": "Yi<PERSON><PERSON>", "description": "A VS Code extension with a blank tool window", "version": "1.0.0", "publisher": "your-publisher-name", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "yike", "title": "Yi<PERSON><PERSON>", "icon": "media/icon.png"}]}, "views": {"yike": [{"id": "yike.yikeView", "name": "Yi<PERSON><PERSON>", "type": "webview", "icon": "media/icon.png", "when": "true"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/node": "^20.11.28", "@types/vscode": "^1.74.0", "@vscode/vsce": "^2.15.0", "cross-env": "^7.0.3", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "dependencies": {"@langchain/baidu-qianfan": "^0.1.0", "@langchain/community": "^0.3.48", "@langchain/core": "^0.3.62", "@langchain/google-genai": "^0.2.14", "@langchain/langgraph": "^0.3.7", "@langchain/langgraph-checkpoint": "^0.0.18", "@langchain/mcp-adapters": "^0.5.3", "@langchain/ollama": "^0.2.3", "@langchain/openai": "^0.5.18", "axios": "^1.10.0", "canvas": "^3.1.2", "dotenv": "^17.2.0", "glob": "^11.0.3", "vue": "^3.5.17", "zod": "^3.25.76"}}