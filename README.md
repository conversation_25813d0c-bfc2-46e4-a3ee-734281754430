# YiKe VS Code Extension

这是一个简单的VS Code插件，安装后在左侧活动栏显示YiKe图标，点击打开空白工具窗口。

## 功能

- 通过命令面板执行 "Open Empty Window" 命令
- 打开一个新的VS Code空白窗口

## 安装

1. 下载 `open-empty-window-1.0.0.vsix` 文件
2. 在VS Code中按 `Ctrl+Shift+P` 打开命令面板
3. 输入并选择 `Extensions: Install from VSIX...`
4. 选择下载的 `.vsix` 文件进行安装

## 使用方法

1. 安装插件后，在VS Code左侧活动栏会出现一个新的图标
2. 点击该图标即可打开空白工具窗口
3. 窗口内容为空白，类似于VS Code的插件安装面板界面

## 开发

```bash
# 安装依赖
npm install

# 编译TypeScript
npm run compile

# 监听文件变化自动编译
npm run watch

# 打包扩展
npm run package
```

## 打包

扩展已经配置好打包命令，执行 `npm run package` 即可生成 `.vsix` 安装包。