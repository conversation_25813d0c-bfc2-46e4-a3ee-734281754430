{"version": "2.0.0", "tasks": [{"type": "npm", "script": "build", "group": "build", "label": "npm: build-webui", "path": "webui", "presentation": {"panel": "shared", "reveal": "silent", "clear": false}, "problemMatcher": ["$tsc"]}, {"type": "npm", "script": "compile", "group": "build", "label": "npm: compile", "presentation": {"panel": "shared", "reveal": "silent", "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "build-all", "dependsOrder": "sequence", "dependsOn": ["npm: build-webui", "npm: compile"], "group": "build", "presentation": {"panel": "shared", "reveal": "silent", "clear": false}}, {"type": "npm", "script": "watch", "group": "build", "label": "npm: watch", "presentation": {"panel": "shared", "reveal": "silent", "clear": false}, "isBackground": true, "problemMatcher": ["$tsc-watch"]}]}