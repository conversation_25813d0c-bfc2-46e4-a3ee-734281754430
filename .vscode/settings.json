{"typescript.preferences.includePackageJsonAutoImports": "off", "typescript.suggest.autoImports": false, "typescript.validate.enable": true, "typescript.format.enable": true, "editor.formatOnSave": true, "files.exclude": {"out": false, "node_modules": true, "**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"out": true, "node_modules": true, "**/node_modules": true}}