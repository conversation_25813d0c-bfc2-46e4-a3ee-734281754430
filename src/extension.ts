import * as vscode from 'vscode';
import { YiKeViewProvider } from './yikeViewProvider';

export function activate(context: vscode.ExtensionContext) {
    console.log('YiKe extension is now active!');

    const provider = new YiKeViewProvider(context.extensionUri);

    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            YiKeViewProvider.viewType,
            provider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        )
    );
}

export function deactivate() {}