import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

export class WebviewHelper {
    constructor(private readonly extensionUri: vscode.Uri) {}

    /**
     * 生成webview的HTML内容
     */
    getHtmlForWebview(webview: vscode.Webview): string {
        try {
            // 获取React构建后的文件路径
            const reactAppPath = vscode.Uri.joinPath(this.extensionUri, 'webui', 'dist');
            const indexHtmlPath = path.join(reactAppPath.fsPath, 'index.html');

            console.log('YiKe: Loading webui from:', indexHtmlPath);

            // 检查文件是否存在
            if (!fs.existsSync(indexHtmlPath)) {
                throw new Error(`构建文件不存在: ${indexHtmlPath}`);
            }

            // 读取构建后的index.html文件
            const indexHtmlContent = fs.readFileSync(indexHtmlPath, 'utf8');
            console.log('YiKe: Index HTML content loaded, length:', indexHtmlContent.length);

            // 解析HTML内容，提取资源文件路径
            const { scriptPath, stylePath } = this.parseAssetPaths(indexHtmlContent);
            console.log('YiKe: Parsed asset paths - script:', scriptPath, 'style:', stylePath);

            if (!scriptPath || !stylePath) {
                throw new Error(`无法从构建文件中解析资源路径 - script: ${scriptPath}, style: ${stylePath}`);
            }

            // 转换为webview URI
            const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(reactAppPath, scriptPath));
            const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(reactAppPath, stylePath));
            console.log('YiKe: Generated URIs - script:', scriptUri.toString(), 'style:', styleUri.toString());

            // 获取VSCode主题变量
            const nonce = this.getNonce();

            return `<!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}' 'unsafe-eval' ${webview.cspSource}; connect-src ${webview.cspSource}; img-src ${webview.cspSource} data: blob:; font-src ${webview.cspSource};">
                    <link href="${styleUri}" rel="stylesheet">
                    <title>YiKe</title>
                </head>
                <body>
                    <div id="root"></div>
                    <script nonce="${nonce}" src="${scriptUri}"></script>
                </body>
                </html>`;
        } catch (error) {
            // 如果解析失败，返回错误页面
            console.error('YiKe: Failed to load webui:', error);
            return this.getErrorHtml(`加载webui失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 比较两个文件
     */
    async compareFiles(filePath1: string, filePath2: string): Promise<void> {
        try {
            // 将相对路径转换为绝对路径
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            const basePath = workspaceFolder ? workspaceFolder.uri.fsPath : process.cwd();
            
            const fullPath1 = path.isAbsolute(filePath1) ? filePath1 : path.join(basePath, filePath1);
            const fullPath2 = path.isAbsolute(filePath2) ? filePath2 : path.join(basePath, filePath2);

            // 检查文件是否存在
            const uri1 = vscode.Uri.file(fullPath1);
            const uri2 = vscode.Uri.file(fullPath2);

            try {
                await vscode.workspace.fs.stat(uri1);
                await vscode.workspace.fs.stat(uri2);
            } catch (error) {
                vscode.window.showErrorMessage(`文件不存在: ${error instanceof Error ? error.message : '未知错误'}`);
                return;
            }

            // 打开VSCode的文件对比窗口
            await vscode.commands.executeCommand('vscode.diff', uri1, uri2, '文件对比');

        } catch (error) {
            vscode.window.showErrorMessage(`文件对比失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 解析HTML内容，提取资源文件路径
     */
    private parseAssetPaths(htmlContent: string): { scriptPath?: string; stylePath?: string } {
        const result: { scriptPath?: string; stylePath?: string } = {};

        // 匹配script标签中的src属性
        const scriptMatch = htmlContent.match(/<script[^>]+src="([^"]+)"[^>]*>/);
        if (scriptMatch && scriptMatch[1]) {
            // 移除开头的斜杠，因为我们需要相对路径
            result.scriptPath = scriptMatch[1].replace(/^\//, '');
        }

        // 匹配link标签中的href属性（CSS文件）
        const styleMatch = htmlContent.match(/<link[^>]+href="([^"]+\.css)"[^>]*>/);
        if (styleMatch && styleMatch[1]) {
            // 移除开头的斜杠，因为我们需要相对路径
            result.stylePath = styleMatch[1].replace(/^\//, '');
        }

        return result;
    }

    /**
     * 生成错误页面HTML
     */
    private getErrorHtml(errorMessage: string): string {
        return `<!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>加载错误</title>
                <style>
                    body {
                        margin: 20px;
                        font-family: Arial, sans-serif;
                        color: #d73a49;
                        background-color: var(--vscode-editor-background);
                    }
                    .error {
                        border: 1px solid #d73a49;
                        padding: 15px;
                        background: var(--vscode-inputValidation-errorBackground);
                        border-radius: 4px;
                    }
                    h2 { margin-top: 0; }
                </style>
            </head>
            <body>
                <div class="error">
                    <h2>加载失败</h2>
                    <p>${errorMessage}</p>
                    <p>请确保webui已正确构建，运行 <code>npm run build</code> 在webui目录下。</p>
                </div>
            </body>
            </html>`;
    }

    /**
     * 生成随机nonce值
     */
    private getNonce(): string {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
}
