import * as vscode from 'vscode';
import { exec } from 'child_process';
import { promisify } from 'util';

export interface CommandResult {
    exitCode: number;
    stdout: string;
    stderr: string;
}

export interface CommandOptions {
    cwd?: string;
    shell?: boolean;
    env?: NodeJS.ProcessEnv;
}

const execAsync = promisify(exec);

const terminals = new Map<vscode.Terminal, vscode.TerminalShellIntegration>();

vscode.window.onDidChangeTerminalShellIntegration(async ({ terminal, shellIntegration }) => {
	terminals.set(terminal, shellIntegration);
});

async function getTerminalShellIntegration(terminal: vscode.Terminal): Promise<vscode.TerminalShellIntegration | undefined> {
    let maxRetry = 10;
    let retries = 0;
	while (!terminals.has(terminal) && retries < maxRetry) {
		await new Promise(resolve => setTimeout(resolve, 100));
        retries++;
	}
	return terminals.get(terminal);
}

export class CommandExecutor {
    private isExecuting: boolean = false;
    private onOutput?: (data: string) => void;
    private onError?: (data: string) => void;
    private onStart?: (processId: string) => void;
    private onComplete?: (exitCode: number) => void;
    private onTerminate?: (processId: string) => void;

    constructor(
        private extensionUri: vscode.Uri,
        private webviewView?: vscode.WebviewView
    ) {}

    setCallbacks(callbacks: {
        onOutput?: (data: string) => void;
        onError?: (data: string) => void;
        onStart?: (processId: string) => void;
        onComplete?: (exitCode: number) => void;
        onTerminate?: (processId: string) => void;
    }) {
        this.onOutput = callbacks.onOutput;
        this.onError = callbacks.onError;
        this.onStart = callbacks.onStart;
        this.onComplete = callbacks.onComplete;
        this.onTerminate = callbacks.onTerminate;
    }

    async execute(command: string): Promise<void> {
        if (!command.trim()) {
            throw new Error('命令不能为空');
        }

        if (this.isExecuting) {
            throw new Error('已有命令正在执行中');
        }

        const cwd = this.getWorkingDirectory();
        const processId = Date.now().toString();

        // 获取或创建终端并显示命令
        const term = this.getOrCreateTerminal(cwd);
        term.show();
        // terminal.sendText(command);

        const shellIntegration = await getTerminalShellIntegration(term);

        // 使用阻塞式执行获取结果
        try {
            // 通知开始
            this.onStart?.(processId);
            this.isExecuting = true;
            let stdout = "";

            if (shellIntegration) {
                const executeCommand = shellIntegration.executeCommand(command);
                const stream = executeCommand.read();
                for await (const data of stream) {
                    stdout += data;
                    console.log(">> " + stdout)
                }
            }

            // const result = await this.executeCommandBlocking(command, cwd);
            const result = {
                stdout,
                stderr: null,
                exitCode: 0,
            }

            // 发送输出
            if (result.stdout) {
                this.onOutput?.(result.stdout);
            }
            if (result.stderr) {
                this.onError?.(result.stderr);
            }

            // 通知完成
            this.onComplete?.(result.exitCode);

        } catch (error) {
            this.onError?.(error instanceof Error ? error.message : String(error));
            this.onComplete?.(1);
        } finally {
            this.isExecuting = false;
        }
    }

    private async executeCommandBlocking(command: string, cwd: string): Promise<CommandResult> {
        try {
            const { stdout, stderr } = await execAsync(command, {
                cwd,
                maxBuffer: 1024 * 1024 * 10, // 10MB buffer
                timeout: 300000 // 5分钟超时
            });

            return {
                exitCode: 0,
                stdout: stdout || '',
                stderr: stderr || ''
            };
        } catch (error: any) {
            return {
                exitCode: error.code || 1,
                stdout: error.stdout || '',
                stderr: error.stderr || error.message || ''
            };
        }
    }

    terminate(): void {
        // 阻塞式执行无法中途终止，只能等待完成
        if (this.isExecuting) {
            console.log('YiKe: 命令正在执行中，无法终止阻塞式命令');
            this.onTerminate?.('blocking-command');
        }
    }

    isRunning(): boolean {
        return this.isExecuting;
    }

    private getWorkingDirectory(): string {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        return workspaceFolder ? workspaceFolder.uri.fsPath : process.cwd();
    }

    private getOrCreateTerminal(cwd: string): vscode.Terminal {
        let terminal = vscode.window.terminals.find(t => t.name === 'YiKe Command');
        
        if (!terminal) {
            const iconUri = vscode.Uri.joinPath(this.extensionUri, 'media', 'icon-32x32.png');
            terminal = vscode.window.createTerminal({
                name: 'YiKe Command',
                cwd,
                iconPath: iconUri
            });
        } else {
            // 更新工作目录
            terminal.sendText(`cd "${cwd}"`);
        }
        
        return terminal;
    }


}