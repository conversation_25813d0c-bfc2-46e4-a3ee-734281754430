import { HumanMessage } from "@langchain/core/messages";
import { MemorySaver } from "@langchain/langgraph-checkpoint";
import { createCustomAgent } from './agent/agent.js';
import {
    MessageTypeGuards,
    ToolCallStatus
} from './agent/agent-stream/index.js';
import { defaultTransformer, transformStream } from './agent/stream-transformer.js';
import { Command } from "@langchain/langgraph";


export interface AIMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    toolCalls?: ToolCall[];
}

export interface ToolCall {
    id: string;
    name: string;
    parameters: Record<string, any>;
    result?: string;
    status: 'pending' | 'success' | 'error';
}

export interface AIServiceCallbacks {
    onMessageStart: (messageId: string) => void;
    onMessageChunk: (messageId: string, chunk: string) => void;
    onMessageComplete: (message: AIMessage) => void;
    onError: (error: string) => void;
    onToolCallStart: (toolCall: ToolCall) => void;
    onToolCallComplete: (toolCall: ToolCall) => void;
}

export class RealAIService {
    private callbacks?: AIServiceCallbacks;
    private agent: any;
    private checkpointer: MemorySaver;
    private conversationId: string = 'default-conversation';
    private messageId: string;
    private currentToolCalls: ToolCall[] = [];

    constructor() {
        this.checkpointer = new MemorySaver();
        this.initializeAgent();
    }

    private async initializeAgent() {
        try {
            this.agent = await createCustomAgent(this.checkpointer);
            console.log('✅ Real AI Agent initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize Real AI Agent:', error);
        }
    }

    public setCallbacks(callbacks: AIServiceCallbacks) {
        this.callbacks = callbacks;
    }

    public async sendMessage(userMessage: string): Promise<void> {
        try {
            if (!this.agent) {
                throw new Error('AI Agent not initialized');
            }

            // 生成消息ID
            this.messageId = Date.now().toString();

            console.log('📨 Sending message to AI Agent:', userMessage);

            // 通知开始处理
            this.callbacks?.onMessageStart(this.messageId);

            // 创建用户消息
            const humanMessage = new HumanMessage(userMessage);

            // 配置
            const config = {
                configurable: {
                    thread_id: this.conversationId,
                },
            };

            // 流式模式
            const streamMode = ["updates", "messages", "custom"];

            // 获取agent流
            const stream = await this.agent.stream(
                { messages: [humanMessage] },
                {
                    ...config,
                    streamMode: streamMode,
                }
            );

            // 处理流式数据
            await this.processAgentStream(stream, this.messageId);

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            console.error('Real AI Service Error:', error);
            this.callbacks?.onError(errorMessage);
        }
    }

    public async acceptToolCall(message: any): Promise<void> {
        try {
            if (!this.agent) {
                throw new Error('AI Agent not initialized');
            }

            // 配置
            const config = {
                configurable: {
                    thread_id: this.conversationId,
                },
            };

            // 流式模式
            const streamMode = ["updates", "messages", "custom"];

            // 获取agent流
            const stream = await this.agent.stream(
                new Command({ resume: { type: "accept" } }),
                {
                    ...config,
                    streamMode: streamMode,
                }
            );

            // 处理流式数据
            await this.processAgentStream(stream, this.messageId);

        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            console.error('Real AI Service Error:', error);
            this.callbacks?.onError(errorMessage);
        }
    }

    private async processAgentStream(stream: any, messageId: string): Promise<void> {
        const metaData = {
            conversionId: this.conversationId,
            messageId: messageId,
        };

        // 使用agent-stream的转换器
        const transformedStream = transformStream(stream, defaultTransformer, metaData);

        let aiResponseContent = '';

        try {
            for await (const chunk of transformedStream) {
                console.log('🔍 Processing stream chunk:', chunk.messageType || 'unknown', chunk);

                if (MessageTypeGuards.isAgentThinkingMessage(chunk)) {
                    // AI思考过程 - 可以显示思考状态
                    console.log('Agent thinking:', chunk.data.content.text);

                } else if (MessageTypeGuards.isToolCallRequestMessage(chunk)) {
                    // 工具调用请求
                    console.log('🔧 Tool call request detected:', chunk.data.toolCall);
                    const toolCall: ToolCall = {
                        id: chunk.data.toolCall.id,
                        name: chunk.data.toolCall.name,
                        parameters: chunk.data.toolCall.args,
                        status: 'pending'
                    };
                    this.currentToolCalls.push(toolCall);
                    console.log('📤 Calling onToolCallStart with:', toolCall);
                    this.callbacks?.onToolCallStart(toolCall);

                } else if (MessageTypeGuards.isToolCallResultMessage(chunk)) {
                    // 工具调用结果
                    console.log('✅ Tool call result detected:', chunk.data.toolResult);
                    const toolResult = chunk.data.toolResult;
                    const toolCall = this.currentToolCalls.find(tc => tc.id === toolResult.toolCallId);
                    
                    if (toolCall) {
                        toolCall.result = toolResult.result;
                        toolCall.status = toolResult.status === ToolCallStatus.SUCCESS ? 'success' : 'error';
                        console.log('📤 Calling onToolCallComplete with:', toolCall);
                        this.callbacks?.onToolCallComplete(toolCall);
                        this.currentToolCalls.splice(this.currentToolCalls.indexOf(toolCall), 1);
                    } else {
                        console.log('❌ Tool call not found for result:', toolResult.toolCallId);
                    }

                } else if (MessageTypeGuards.isAgentResponseMessage(chunk)) {
                    // AI响应内容
                    const content = chunk.data.content.text;
                    aiResponseContent += content;
                    this.callbacks?.onMessageChunk(messageId, content);

                } else if (MessageTypeGuards.isMessageEndMessage(chunk)) {
                    // 消息结束
                    console.log('Message completed:', chunk.data.messageComplete);
                    // 创建最终的AI消息对象
                    const aiMessage: AIMessage = {
                        id: messageId,
                        role: 'assistant',
                        content: aiResponseContent || '抱歉，我没有生成任何回复。',
                        timestamp: new Date(),
                        toolCalls: this.currentToolCalls.length > 0 ? this.currentToolCalls : undefined
                    };

                    // 通知消息完成
                    this.callbacks?.onMessageComplete(aiMessage);
                            break;
                    }
            }

        } catch (streamError) {
            console.error('Error processing stream:', streamError);
            throw streamError;
        }
    }

    // 设置对话ID
    public setConversationId(conversationId: string) {
        this.conversationId = conversationId;
    }

    // 清除对话历史
    public async clearConversation() {
        try {
            // 清除checkpointer中的对话历史
            // 注意：MemorySaver可能没有直接的清除方法，这里我们重新创建一个新的实例
            this.checkpointer = new MemorySaver();
            this.agent = await createCustomAgent(this.checkpointer);
            console.log('✅ Conversation cleared and agent reinitialized');
        } catch (error) {
            console.error('❌ Failed to clear conversation:', error);
        }
    }

    // 获取对话历史
    public async getConversationHistory(): Promise<any[]> {
        try {
            // 这里可以实现获取对话历史的逻辑
            // 具体实现取决于checkpointer的API
            return [];
        } catch (error) {
            console.error('❌ Failed to get conversation history:', error);
            return [];
        }
    }
}
