import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { promisify } from 'util';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

export interface SnapshotInfo {
    id: string;
    filePath: string;
    timestamp: Date;
    hash: string;
    size: number;
    description?: string;
}

export interface SnapshotMetadata {
    snapshots: SnapshotInfo[];
    threadId: string;
    workspaceRoot: string;
}

/**
 * 工程文件快照管理器
 * 为智能体会话提供文件快照功能，支持多版本管理
 */
export class SnapshotManager {
    private threadId: string;
    private workspaceRoot: string;
    private snapshotDir: string;
    private metadataFile: string;
    private metadata: SnapshotMetadata;

    constructor(threadId: string, workspaceRoot: string) {
        this.threadId = threadId;
        this.workspaceRoot = workspaceRoot;
        
        // 获取用户主目录下的 .yike 目录
        const homeDir = require('os').homedir();
        const yikeDir = path.join(homeDir, '.yike');
        
        // 为每个会话创建独立的快照目录
        this.snapshotDir = path.join(yikeDir, 'snapshots', this.threadId);
        this.metadataFile = path.join(this.snapshotDir, 'metadata.json');
        
        this.metadata = {
            snapshots: [],
            threadId: this.threadId,
            workspaceRoot: this.workspaceRoot
        };
    }

    /**
     * 初始化快照管理器
     */
    async initialize(): Promise<void> {
        try {
            // 确保快照目录存在
            await mkdir(this.snapshotDir, { recursive: true });
            
            // 尝试加载现有的元数据
            if (fs.existsSync(this.metadataFile)) {
                const metadataContent = await readFile(this.metadataFile, 'utf8');
                this.metadata = JSON.parse(metadataContent);
                
                // 确保元数据中的日期对象正确解析
                this.metadata.snapshots.forEach(snapshot => {
                    snapshot.timestamp = new Date(snapshot.timestamp);
                });
            }
            
            console.log(`SnapshotManager initialized for thread ${this.threadId}`);
        } catch (error) {
            console.error('Failed to initialize SnapshotManager:', error);
            throw error;
        }
    }

    /**
     * 创建文件快照
     */
    async createSnapshot(filePath: string, description?: string): Promise<SnapshotInfo> {
        try {
            // 将相对路径转换为绝对路径
            const absolutePath = path.isAbsolute(filePath) ? filePath : path.join(this.workspaceRoot, filePath);
            
            // 检查文件是否存在
            if (!fs.existsSync(absolutePath)) {
                throw new Error(`File does not exist: ${filePath}`);
            }
            
            // 读取文件内容
            const fileContent = await readFile(absolutePath, 'utf8');
            const fileStats = await stat(absolutePath);
            
            // 计算文件内容的哈希值
            const hash = crypto.createHash('sha256').update(fileContent).digest('hex');
            
            // 生成快照ID
            const snapshotId = `${Date.now()}_${hash.substring(0, 8)}`;
            
            // 创建快照信息
            const snapshotInfo: SnapshotInfo = {
                id: snapshotId,
                filePath: path.relative(this.workspaceRoot, absolutePath),
                timestamp: new Date(),
                hash: hash,
                size: fileStats.size,
                description: description
            };
            
            // 保存快照文件
            const snapshotFilePath = path.join(this.snapshotDir, `${snapshotId}.snapshot`);
            await writeFile(snapshotFilePath, fileContent, 'utf8');
            
            // 更新元数据
            this.metadata.snapshots.push(snapshotInfo);
            await this.saveMetadata();
            
            console.log(`Created snapshot ${snapshotId} for file ${filePath}`);
            return snapshotInfo;
            
        } catch (error) {
            console.error(`Failed to create snapshot for ${filePath}:`, error);
            throw error;
        }
    }

    /**
     * 获取文件的所有快照
     */
    getFileSnapshots(filePath: string): SnapshotInfo[] {
        const relativePath = path.isAbsolute(filePath) 
            ? path.relative(this.workspaceRoot, filePath)
            : filePath;
            
        return this.metadata.snapshots
            .filter(snapshot => snapshot.filePath === relativePath)
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()); // 按时间倒序
    }

    /**
     * 获取所有快照文件列表
     */
    getAllSnapshots(): SnapshotInfo[] {
        return [...this.metadata.snapshots].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    }

    /**
     * 获取快照内容
     */
    async getSnapshotContent(snapshotId: string): Promise<string> {
        try {
            const snapshotFilePath = path.join(this.snapshotDir, `${snapshotId}.snapshot`);
            
            if (!fs.existsSync(snapshotFilePath)) {
                throw new Error(`Snapshot file not found: ${snapshotId}`);
            }
            
            return await readFile(snapshotFilePath, 'utf8');
        } catch (error) {
            console.error(`Failed to get snapshot content for ${snapshotId}:`, error);
            throw error;
        }
    }

    /**
     * 删除快照
     */
    async deleteSnapshot(snapshotId: string): Promise<void> {
        try {
            // 从元数据中移除
            const index = this.metadata.snapshots.findIndex(s => s.id === snapshotId);
            if (index === -1) {
                throw new Error(`Snapshot not found: ${snapshotId}`);
            }
            
            this.metadata.snapshots.splice(index, 1);
            
            // 删除快照文件
            const snapshotFilePath = path.join(this.snapshotDir, `${snapshotId}.snapshot`);
            if (fs.existsSync(snapshotFilePath)) {
                await unlink(snapshotFilePath);
            }
            
            // 保存元数据
            await this.saveMetadata();
            
            console.log(`Deleted snapshot ${snapshotId}`);
        } catch (error) {
            console.error(`Failed to delete snapshot ${snapshotId}:`, error);
            throw error;
        }
    }

    /**
     * 删除文件的所有快照
     */
    async deleteFileSnapshots(filePath: string): Promise<void> {
        const snapshots = this.getFileSnapshots(filePath);
        
        for (const snapshot of snapshots) {
            await this.deleteSnapshot(snapshot.id);
        }
        
        console.log(`Deleted all snapshots for file ${filePath}`);
    }

    /**
     * 获取文件的最新快照
     */
    getLatestSnapshot(filePath: string): SnapshotInfo | null {
        const snapshots = this.getFileSnapshots(filePath);
        return snapshots.length > 0 ? snapshots[0] : null;
    }

    /**
     * 检查文件是否有快照
     */
    hasSnapshots(filePath: string): boolean {
        return this.getFileSnapshots(filePath).length > 0;
    }

    /**
     * 获取快照统计信息
     */
    getStats(): { totalSnapshots: number; totalFiles: number; totalSize: number } {
        const uniqueFiles = new Set(this.metadata.snapshots.map(s => s.filePath));
        const totalSize = this.metadata.snapshots.reduce((sum, s) => sum + s.size, 0);
        
        return {
            totalSnapshots: this.metadata.snapshots.length,
            totalFiles: uniqueFiles.size,
            totalSize: totalSize
        };
    }

    /**
     * 清理所有快照
     */
    async cleanup(): Promise<void> {
        try {
            // 删除所有快照文件
            for (const snapshot of this.metadata.snapshots) {
                const snapshotFilePath = path.join(this.snapshotDir, `${snapshot.id}.snapshot`);
                if (fs.existsSync(snapshotFilePath)) {
                    await unlink(snapshotFilePath);
                }
            }
            
            // 清空元数据
            this.metadata.snapshots = [];
            await this.saveMetadata();
            
            console.log(`Cleaned up all snapshots for thread ${this.threadId}`);
        } catch (error) {
            console.error('Failed to cleanup snapshots:', error);
            throw error;
        }
    }

    /**
     * 保存元数据到文件
     */
    private async saveMetadata(): Promise<void> {
        try {
            const metadataContent = JSON.stringify(this.metadata, null, 2);
            await writeFile(this.metadataFile, metadataContent, 'utf8');
        } catch (error) {
            console.error('Failed to save metadata:', error);
            throw error;
        }
    }
}
