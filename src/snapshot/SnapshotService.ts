import * as vscode from 'vscode';
import { getSnapshotManager } from './SnapshotManagerFactory';
import { SnapshotManager, SnapshotInfo } from './SnapshotManager';

/**
 * 快照服务类
 * 为AI服务和工具调用提供快照功能的高级接口
 */
export class SnapshotService {
    private static instance: SnapshotService;
    private currentThreadId: string | null = null;

    private constructor() {}

    /**
     * 获取快照服务单例实例
     */
    static getInstance(): SnapshotService {
        if (!SnapshotService.instance) {
            SnapshotService.instance = new SnapshotService();
        }
        return SnapshotService.instance;
    }

    /**
     * 设置当前会话的线程ID
     */
    setCurrentThreadId(threadId: string): void {
        this.currentThreadId = threadId;
        console.log(`SnapshotService: Set current thread ID to ${threadId}`);
    }

    /**
     * 获取当前线程的快照管理器
     */
    private async getCurrentManager(): Promise<SnapshotManager> {
        if (!this.currentThreadId) {
            throw new Error('No current thread ID set. Call setCurrentThreadId() first.');
        }
        return await getSnapshotManager(this.currentThreadId);
    }

    /**
     * 在文件修改前创建快照
     * 这个方法应该在文件修改工具调用前被调用
     */
    async createSnapshotBeforeModification(filePath: string, description?: string): Promise<SnapshotInfo | null> {
        try {
            const manager = await this.getCurrentManager();
            
            // 检查文件是否已经有快照
            const existingSnapshots = manager.getFileSnapshots(filePath);
            
            // 如果已经有快照，不重复创建（按照策略，只保存最老版本）
            if (existingSnapshots.length > 0) {
                console.log(`File ${filePath} already has snapshots, skipping creation`);
                return existingSnapshots[existingSnapshots.length - 1]; // 返回最老的快照
            }
            
            // 创建快照
            const snapshot = await manager.createSnapshot(filePath, description || 'Before AI modification');
            console.log(`Created snapshot for file ${filePath} before modification`);
            return snapshot;
            
        } catch (error) {
            console.error(`Failed to create snapshot for ${filePath}:`, error);
            return null;
        }
    }

    /**
     * 获取文件的快照列表
     */
    async getFileSnapshots(filePath: string): Promise<SnapshotInfo[]> {
        try {
            const manager = await this.getCurrentManager();
            return manager.getFileSnapshots(filePath);
        } catch (error) {
            console.error(`Failed to get snapshots for ${filePath}:`, error);
            return [];
        }
    }

    /**
     * 获取所有快照
     */
    async getAllSnapshots(): Promise<SnapshotInfo[]> {
        try {
            const manager = await this.getCurrentManager();
            return manager.getAllSnapshots();
        } catch (error) {
            console.error('Failed to get all snapshots:', error);
            return [];
        }
    }

    /**
     * 获取快照内容
     */
    async getSnapshotContent(snapshotId: string): Promise<string | null> {
        try {
            const manager = await this.getCurrentManager();
            return await manager.getSnapshotContent(snapshotId);
        } catch (error) {
            console.error(`Failed to get snapshot content for ${snapshotId}:`, error);
            return null;
        }
    }

    /**
     * 删除文件的所有快照（当文件变更被接受或撤销后调用）
     */
    async deleteFileSnapshots(filePath: string): Promise<void> {
        try {
            const manager = await this.getCurrentManager();
            await manager.deleteFileSnapshots(filePath);
            console.log(`Deleted all snapshots for file ${filePath}`);
        } catch (error) {
            console.error(`Failed to delete snapshots for ${filePath}:`, error);
        }
    }

    /**
     * 恢复文件到快照版本
     */
    async restoreFileFromSnapshot(filePath: string, snapshotId: string): Promise<boolean> {
        try {
            const manager = await this.getCurrentManager();
            const snapshotContent = await manager.getSnapshotContent(snapshotId);
            
            if (!snapshotContent) {
                throw new Error(`Snapshot content not found for ${snapshotId}`);
            }
            
            // 获取工作区根目录
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                throw new Error('No workspace folder found');
            }
            
            // 构建绝对路径
            const absolutePath = require('path').isAbsolute(filePath) 
                ? filePath 
                : require('path').join(workspaceFolder.uri.fsPath, filePath);
            
            // 写入文件
            const fs = require('fs').promises;
            await fs.writeFile(absolutePath, snapshotContent, 'utf8');
            
            console.log(`Restored file ${filePath} from snapshot ${snapshotId}`);
            return true;
            
        } catch (error) {
            console.error(`Failed to restore file ${filePath} from snapshot ${snapshotId}:`, error);
            return false;
        }
    }

    /**
     * 检查文件是否有快照
     */
    async hasSnapshots(filePath: string): Promise<boolean> {
        try {
            const manager = await this.getCurrentManager();
            return manager.hasSnapshots(filePath);
        } catch (error) {
            console.error(`Failed to check snapshots for ${filePath}:`, error);
            return false;
        }
    }

    /**
     * 获取快照统计信息
     */
    async getStats(): Promise<{ totalSnapshots: number; totalFiles: number; totalSize: number } | null> {
        try {
            const manager = await this.getCurrentManager();
            return manager.getStats();
        } catch (error) {
            console.error('Failed to get snapshot stats:', error);
            return null;
        }
    }

    /**
     * 清理当前会话的所有快照
     */
    async cleanup(): Promise<void> {
        try {
            if (this.currentThreadId) {
                const manager = await this.getCurrentManager();
                await manager.cleanup();
                console.log(`Cleaned up snapshots for thread ${this.currentThreadId}`);
            }
        } catch (error) {
            console.error('Failed to cleanup snapshots:', error);
        }
    }
}

/**
 * 便捷函数：获取快照服务实例
 */
export function getSnapshotService(): SnapshotService {
    return SnapshotService.getInstance();
}
