import * as vscode from 'vscode';
import { SnapshotManager } from './SnapshotManager';

/**
 * 快照管理器工厂类
 * 采用工程模式对管理器进行按需创建，确保不同会话使用不同的管理器实例
 */
export class SnapshotManagerFactory {
    private static instance: SnapshotManagerFactory;
    private managers: Map<string, SnapshotManager> = new Map();

    private constructor() {}

    /**
     * 获取工厂单例实例
     */
    static getInstance(): SnapshotManagerFactory {
        if (!SnapshotManagerFactory.instance) {
            SnapshotManagerFactory.instance = new SnapshotManagerFactory();
        }
        return SnapshotManagerFactory.instance;
    }

    /**
     * 获取或创建指定线程的快照管理器
     */
    async getManager(threadId: string): Promise<SnapshotManager> {
        if (!threadId) {
            throw new Error('Thread ID is required');
        }

        // 如果已存在该线程的管理器，直接返回
        if (this.managers.has(threadId)) {
            return this.managers.get(threadId)!;
        }

        // 获取当前工作区根目录
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder found');
        }

        const workspaceRoot = workspaceFolder.uri.fsPath;

        // 创建新的快照管理器
        const manager = new SnapshotManager(threadId, workspaceRoot);
        await manager.initialize();

        // 缓存管理器实例
        this.managers.set(threadId, manager);

        console.log(`Created new SnapshotManager for thread: ${threadId}`);
        return manager;
    }

    /**
     * 移除指定线程的快照管理器
     */
    async removeManager(threadId: string): Promise<void> {
        if (this.managers.has(threadId)) {
            const manager = this.managers.get(threadId)!;
            
            // 清理快照数据
            await manager.cleanup();
            
            // 从缓存中移除
            this.managers.delete(threadId);
            
            console.log(`Removed SnapshotManager for thread: ${threadId}`);
        }
    }

    /**
     * 获取所有活跃的线程ID
     */
    getActiveThreadIds(): string[] {
        return Array.from(this.managers.keys());
    }

    /**
     * 获取管理器统计信息
     */
    getStats(): { activeManagers: number; threadIds: string[] } {
        return {
            activeManagers: this.managers.size,
            threadIds: this.getActiveThreadIds()
        };
    }

    /**
     * 清理所有管理器
     */
    async cleanup(): Promise<void> {
        const threadIds = this.getActiveThreadIds();
        
        for (const threadId of threadIds) {
            await this.removeManager(threadId);
        }
        
        console.log('Cleaned up all SnapshotManagers');
    }
}

/**
 * 便捷函数：获取指定线程的快照管理器
 */
export async function getSnapshotManager(threadId: string): Promise<SnapshotManager> {
    const factory = SnapshotManagerFactory.getInstance();
    return await factory.getManager(threadId);
}

/**
 * 便捷函数：移除指定线程的快照管理器
 */
export async function removeSnapshotManager(threadId: string): Promise<void> {
    const factory = SnapshotManagerFactory.getInstance();
    await factory.removeManager(threadId);
}
