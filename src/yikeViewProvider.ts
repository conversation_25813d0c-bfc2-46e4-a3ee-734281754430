import * as vscode from 'vscode';
import { CommandExecutor } from './commandExecutor';
import { WebviewHelper } from './webviewHelper';
import { RealAIService } from './realAIService';

export class YiKeViewProvider implements vscode.WebviewViewProvider {

    public static readonly viewType = 'yike.yikeView';

    private _view?: vscode.WebviewView;
    private _commandExecutor: CommandExecutor;
    private _webviewHelper: WebviewHelper;
    private _aiService: RealAIService;
    private _isInitialized: boolean = false;

    constructor(private readonly _extensionUri: vscode.Uri) {
        this._commandExecutor = new CommandExecutor(this._extensionUri);
        this._webviewHelper = new WebviewHelper(this._extensionUri);
        this._aiService = new RealAIService();
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ) {
        this._view = webviewView;

        // 配置webview选项
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri,
                vscode.Uri.joinPath(this._extensionUri, 'webui', 'dist')
            ],
            enableCommandUris: true
        };

        // 检查是否是首次初始化或者需要重新加载
        const shouldReload = !this._isInitialized || !context.state;

        if (shouldReload) {
            console.log('YiKe: Initializing webview content');
            webviewView.webview.html = this._webviewHelper.getHtmlForWebview(webviewView.webview);
            this._isInitialized = true;
        } else {
            console.log('YiKe: Webview already initialized, preserving content');
        }

        // 只在首次初始化时设置回调，避免重复设置
        if (shouldReload) {
            this.setupCallbacks(webviewView);
        }
    }

    private setupCallbacks(webviewView: vscode.WebviewView) {
        // 设置命令执行器的回调
        this._commandExecutor.setCallbacks({
            onStart: (processId: string) => {
                this._view?.webview.postMessage({
                    type: 'commandStart',
                    processId: processId
                });
            },
            onOutput: (data: string) => {
                this._view?.webview.postMessage({
                    type: 'commandOutput',
                    content: data
                });
            },
            onError: (data: string) => {
                this._view?.webview.postMessage({
                    type: 'commandError',
                    content: data
                });
            },
            onComplete: (exitCode: number) => {
                this._view?.webview.postMessage({
                    type: 'commandComplete',
                    exitCode: exitCode
                });
            },
            onTerminate: (processId: string) => {
                this._view?.webview.postMessage({
                    type: 'commandTerminated',
                    processId: processId
                });
            }
        });

        // 设置AI服务的回调
        this._aiService.setCallbacks({
            onMessageStart: (messageId: string) => {
                this._view?.webview.postMessage({
                    type: 'aiMessageStart',
                    messageId: messageId
                });
            },
            onMessageChunk: (messageId: string, chunk: string) => {
                this._view?.webview.postMessage({
                    type: 'aiMessageChunk',
                    messageId: messageId,
                    chunk: chunk
                });
            },
            onMessageComplete: (message: any) => {
                this._view?.webview.postMessage({
                    type: 'aiMessageComplete',
                    message: message
                });
            },
            onError: (error: string) => {
                this._view?.webview.postMessage({
                    type: 'aiError',
                    error: error
                });
            },
            onToolCallStart: (toolCall: any) => {
                console.log('🚀 ViewProvider: Sending aiToolCallStart to webview:', toolCall);
                this._view?.webview.postMessage({
                    type: 'aiToolCallStart',
                    toolCall: toolCall
                });
            },
            onToolCallComplete: (toolCall: any) => {
                console.log('🏁 ViewProvider: Sending aiToolCallComplete to webview:', toolCall);
                this._view?.webview.postMessage({
                    type: 'aiToolCallComplete',
                    toolCall: toolCall
                });
            }
        });

        // 处理来自前端的消息
        webviewView.webview.onDidReceiveMessage(async (message: any) => {
            switch (message.type) {
                case 'executeCommand':
                    await this._commandExecutor.execute(message.command);
                    break;
                case 'terminateCommand':
                    this._commandExecutor.terminate();
                    break;
                case 'compareFiles':
                    await this._webviewHelper.compareFiles(message.file1, message.file2);
                    break;
                case 'sendAIMessage':
                    await this._aiService.sendMessage(message.message);
                    break;
                case 'acceptToolCall':
                    console.log('✅ Accepting tool call:', message);
                    await this._aiService.acceptToolCall(message);
                    break;
                case 'acceptFileChange':
                    await this.handleAcceptFileChange(message.filePath);
                    break;
                case 'revertFileChange':
                    await this.handleRevertFileChange(message.filePath, message.snapshotId);
                    break;
                case 'compareFileWithSnapshot':
                    await this.handleCompareFileWithSnapshot(message.filePath, message.snapshotId);
                    break;

            }
        });
    }

    /**
     * 处理接受文件变更
     */
    private async handleAcceptFileChange(filePath: string): Promise<void> {
        try {
            const { getSnapshotService } = await import('./snapshot/SnapshotService.js');
            const snapshotService = getSnapshotService();

            // 删除文件的快照（因为变更已被接受）
            await snapshotService.deleteFileSnapshots(filePath);

            // 通知前端文件变更已被接受
            this._view?.webview.postMessage({
                type: 'fileChangeAccepted',
                filePath: filePath
            });

            console.log(`File change accepted for: ${filePath}`);
        } catch (error) {
            console.error(`Failed to accept file change for ${filePath}:`, error);
            vscode.window.showErrorMessage(`接受文件变更失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 处理撤销文件变更
     */
    private async handleRevertFileChange(filePath: string, snapshotId: string): Promise<void> {
        try {
            const { getSnapshotService } = await import('./snapshot/SnapshotService.js');
            const snapshotService = getSnapshotService();

            // 恢复文件到快照版本
            const success = await snapshotService.restoreFileFromSnapshot(filePath, snapshotId);

            if (success) {
                // 删除文件的快照（因为已恢复）
                await snapshotService.deleteFileSnapshots(filePath);

                // 通知前端文件变更已被撤销
                this._view?.webview.postMessage({
                    type: 'fileChangeReverted',
                    filePath: filePath
                });

                vscode.window.showInformationMessage(`文件已恢复到快照版本: ${filePath}`);
                console.log(`File reverted to snapshot for: ${filePath}`);
            } else {
                throw new Error('Failed to restore file from snapshot');
            }
        } catch (error) {
            console.error(`Failed to revert file change for ${filePath}:`, error);
            vscode.window.showErrorMessage(`撤销文件变更失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 处理文件与快照的对比
     */
    private async handleCompareFileWithSnapshot(filePath: string, snapshotId: string): Promise<void> {
        try {
            const { getSnapshotService } = await import('./snapshot/SnapshotService.js');
            const snapshotService = getSnapshotService();

            // 获取快照内容
            const snapshotContent = await snapshotService.getSnapshotContent(snapshotId);

            if (!snapshotContent) {
                throw new Error('Snapshot content not found');
            }

            // 创建临时文件来存储快照内容
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                throw new Error('No workspace folder found');
            }

            const path = require('path');
            const fs = require('fs').promises;
            const os = require('os');

            const tempDir = os.tmpdir();
            const tempFileName = `${path.basename(filePath)}.snapshot.${snapshotId.substring(0, 8)}`;
            const tempFilePath = path.join(tempDir, tempFileName);

            // 写入快照内容到临时文件
            await fs.writeFile(tempFilePath, snapshotContent, 'utf8');

            // 构建当前文件的绝对路径
            const currentFilePath = path.isAbsolute(filePath)
                ? filePath
                : path.join(workspaceFolder.uri.fsPath, filePath);

            // 打开VSCode的文件对比窗口
            const currentFileUri = vscode.Uri.file(currentFilePath);
            const snapshotFileUri = vscode.Uri.file(tempFilePath);

            await vscode.commands.executeCommand(
                'vscode.diff',
                snapshotFileUri,
                currentFileUri,
                `${path.basename(filePath)} (快照 ↔ 当前)`
            );

            console.log(`Opened file comparison for: ${filePath}`);
        } catch (error) {
            console.error(`Failed to compare file with snapshot for ${filePath}:`, error);
            vscode.window.showErrorMessage(`打开文件对比失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
}