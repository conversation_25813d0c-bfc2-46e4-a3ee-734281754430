import * as vscode from 'vscode';
import { CommandExecutor } from './commandExecutor';
import { WebviewHelper } from './webviewHelper';
import { RealAIService } from './realAIService';

export class YiKeViewProvider implements vscode.WebviewViewProvider {

    public static readonly viewType = 'yike.yikeView';

    private _view?: vscode.WebviewView;
    private _commandExecutor: CommandExecutor;
    private _webviewHelper: WebviewHelper;
    private _aiService: RealAIService;
    private _isInitialized: boolean = false;

    constructor(private readonly _extensionUri: vscode.Uri) {
        this._commandExecutor = new CommandExecutor(this._extensionUri);
        this._webviewHelper = new WebviewHelper(this._extensionUri);
        this._aiService = new RealAIService();
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ) {
        this._view = webviewView;

        // 配置webview选项
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri,
                vscode.Uri.joinPath(this._extensionUri, 'webui', 'dist')
            ],
            enableCommandUris: true
        };

        // 检查是否是首次初始化或者需要重新加载
        const shouldReload = !this._isInitialized || !context.state;

        if (shouldReload) {
            console.log('YiKe: Initializing webview content');
            webviewView.webview.html = this._webviewHelper.getHtmlForWebview(webviewView.webview);
            this._isInitialized = true;
        } else {
            console.log('YiKe: Webview already initialized, preserving content');
        }

        // 只在首次初始化时设置回调，避免重复设置
        if (shouldReload) {
            this.setupCallbacks(webviewView);
        }
    }

    private setupCallbacks(webviewView: vscode.WebviewView) {
        // 设置命令执行器的回调
        this._commandExecutor.setCallbacks({
            onStart: (processId: string) => {
                this._view?.webview.postMessage({
                    type: 'commandStart',
                    processId: processId
                });
            },
            onOutput: (data: string) => {
                this._view?.webview.postMessage({
                    type: 'commandOutput',
                    content: data
                });
            },
            onError: (data: string) => {
                this._view?.webview.postMessage({
                    type: 'commandError',
                    content: data
                });
            },
            onComplete: (exitCode: number) => {
                this._view?.webview.postMessage({
                    type: 'commandComplete',
                    exitCode: exitCode
                });
            },
            onTerminate: (processId: string) => {
                this._view?.webview.postMessage({
                    type: 'commandTerminated',
                    processId: processId
                });
            }
        });

        // 设置AI服务的回调
        this._aiService.setCallbacks({
            onMessageStart: (messageId: string) => {
                this._view?.webview.postMessage({
                    type: 'aiMessageStart',
                    messageId: messageId
                });
            },
            onMessageChunk: (messageId: string, chunk: string) => {
                this._view?.webview.postMessage({
                    type: 'aiMessageChunk',
                    messageId: messageId,
                    chunk: chunk
                });
            },
            onMessageComplete: (message: any) => {
                this._view?.webview.postMessage({
                    type: 'aiMessageComplete',
                    message: message
                });
            },
            onError: (error: string) => {
                this._view?.webview.postMessage({
                    type: 'aiError',
                    error: error
                });
            },
            onToolCallStart: (toolCall: any) => {
                console.log('🚀 ViewProvider: Sending aiToolCallStart to webview:', toolCall);
                this._view?.webview.postMessage({
                    type: 'aiToolCallStart',
                    toolCall: toolCall
                });
            },
            onToolCallComplete: (toolCall: any) => {
                console.log('🏁 ViewProvider: Sending aiToolCallComplete to webview:', toolCall);
                this._view?.webview.postMessage({
                    type: 'aiToolCallComplete',
                    toolCall: toolCall
                });
            }
        });

        // 处理来自前端的消息
        webviewView.webview.onDidReceiveMessage(async (message: any) => {
            switch (message.type) {
                case 'executeCommand':
                    await this._commandExecutor.execute(message.command);
                    break;
                case 'terminateCommand':
                    this._commandExecutor.terminate();
                    break;
                case 'compareFiles':
                    await this._webviewHelper.compareFiles(message.file1, message.file2);
                    break;
                case 'sendAIMessage':
                    await this._aiService.sendMessage(message.message);
                    break;
                case 'acceptToolCall':
                    console.log('✅ Accepting tool call:', message);
                    await this._aiService.acceptToolCall(message);
                    break;

            }
        });
    }
}