import { AIMessageChunk, HumanMessage } from "@langchain/core/messages";
import { writeMessageLog } from "./utils.js";
import { MessageFactory, ToolCallStatus } from "./agent-stream/index.js";
import { ToolMessage } from "@langchain/core/messages";

/**
 * Stream转换模块
 * 将输入流中的chunk逐个转换，返回新的异步生成器
 */
export async function* transformStream(
  inputStream: AsyncIterable<any>,
  transformer?: (chunk: any, metaData: any) => any,
  metaData?: any
): AsyncGenerator<any, void, unknown> {
  for await (const chunk of inputStream) {
    writeMessageLog(chunk);
    // 如果提供了转换函数，使用它；否则原样返回
    const transformedChunk = transformer ? transformer(chunk, metaData) : chunk;
    if (!transformedChunk) {
      continue;
    }
    writeMessageLog(transformedChunk, "/tmp/transformed-message.txt");
    yield transformedChunk;
  }
}

/**
 * 默认转换函数 - 原样返回
 */
export function defaultTransformer(chunk: any, metaData: any): any {
  if(chunk[0] === "updates" && (chunk[1]?.start?.messages?.[0] instanceof HumanMessage)) {
    return MessageFactory.createHumanMessage(
      metaData,
      chunk[1].start.messages[0].text,
    );
  }

  if(chunk[0] === "messages" && chunk[1]?.length && chunk[1][0] instanceof AIMessageChunk ) {
    const content = chunk[1][0].content;
    if (!content || content === '') {
      return null;
    }
    return MessageFactory.createAgentResponseMessage(
      metaData,
      typeof content === "string" ? content : JSON.stringify(content)
    );
  }

  if(chunk[0] === "updates" && (chunk[1]?.final)) {
    return MessageFactory.createMessageEndMessage(
      metaData,
    );
  }

  if(chunk[0] === "updates" && chunk[1]?.__interrupt__?.length) {
    return MessageFactory.createToolCallRequestMessage(
      metaData,
      {
        id: chunk[1].__interrupt__[0].value[1].toolCallId,
        tool: chunk[1].__interrupt__[0].value[0].action_request.action,
        name: chunk[1].__interrupt__[0].value[0].action_request.action,
        args: chunk[1].__interrupt__[0].value[0].action_request.args,
      }
    );
  }

  if(chunk[0] === "messages" && chunk[1][0] instanceof ToolMessage) {
    return MessageFactory.createToolCallResultMessage(
      metaData,
      {
        toolCallId: chunk[1][0].tool_call_id,
        tool: chunk[1][0].name || 'unknown',
        status: ToolCallStatus.SUCCESS,
        result: chunk[1][0].content
      }
    );
  }


  return null;
}