import {
  BaseCheckpointSaver,
  CheckpointTuple,
  copyCheckpoint,
  getCheckpointId,
  TASKS,
} from "@langchain/langgraph-checkpoint";
import axios, { AxiosInstance } from "axios";
import { promises as fs } from "fs";

export class RESTSaver extends BaseCheckpointSaver {
  private apiClient: AxiosInstance;

  /**
   * 将对象形式的字节数组转换为 Uint8Array
   * REST API 返回的数据格式是 {0: 123, 1: 34, 2: 118, ...}
   * 需要转换为 Uint8Array 供 LangChain 序列化器使用
   */
  private objectToUint8Array(obj: any): Uint8Array {
    if (obj instanceof Uint8Array || obj instanceof ArrayBuffer) {
      return obj instanceof ArrayBuffer ? new Uint8Array(obj) : obj;
    }

    if (typeof obj === "string") {
      return new TextEncoder().encode(obj);
    }

    // 处理对象形式的字节数组 {0: 123, 1: 34, ...}
    if (typeof obj === "object" && obj !== null) {
      const keys = Object.keys(obj)
        .map(Number)
        .sort((a, b) => a - b);
      const bytes = new Uint8Array(keys.length);
      for (let i = 0; i < keys.length; i++) {
        bytes[i] = obj[keys[i]];
      }
      return bytes;
    }

    throw new Error(
      `Unsupported data type for conversion to Uint8Array: ${typeof obj}`
    );
  }

  constructor(
    baseURL: string = "http://localhost:5000/checkpoint",
    token: string,  
    serde?: any
  ) {
    super(serde);

    this.apiClient = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    });

    // 添加请求拦截器用于日志
    this.apiClient.interceptors.request.use(
      (config) => {
        console.log(
          `RESTSaver API Request: ${config.method?.toUpperCase()} ${config.url}`
        );
        return config;
      },
      (error) => {
        console.error("RESTSaver Request Error:", error);
        return Promise.reject(error);
      }
    );

    // 添加响应拦截器用于错误处理
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error(
          "RESTSaver Response Error:",
          error.response?.data || error.message
        );
        return Promise.reject(error);
      }
    );
  }

  /**
   * 获取待处理的发送操作
   */
  async _getPendingSends(
    threadId: string,
    checkpointNs: string,
    parentCheckpointId?: string
  ): Promise<any[]> {
    let pendingSends = [];

    if (parentCheckpointId !== undefined) {
      try {
        const response = await this.apiClient.get("/writes", {
          params: {
            thread_id: threadId,
            checkpoint_ns: checkpointNs,
            checkpoint_id: parentCheckpointId,
          },
        });

        pendingSends = await Promise.all(
          response.data
            .filter((write: any) => write.channel === TASKS)
            .map(async (write: any) => {
              return this.serde.loadsTyped(
                "json",
                this.objectToUint8Array(write.value)
              );
            })
        );
      } catch (error) {
        console.error("Error fetching pending sends:", error);
      }
    }

    return pendingSends;
  }

  /**
   * 获取检查点元组
   */
  async getTuple(config: any) {
    const thread_id = config.configurable?.thread_id;
    const checkpoint_ns = config.configurable?.checkpoint_ns ?? "";
    let checkpoint_id = getCheckpointId(config);

    if (!thread_id) {
      console.warn("No thread_id provided in config");
      return undefined;
    }

    try {
      const params: any = {
        thread_id,
        checkpoint_ns,
      };

      if (checkpoint_id) {
        params.checkpoint_id = checkpoint_id;
      }

      const response = await this.apiClient.get("", { params });

      if (response.status === 200 && response.data) {
        const saved = response.data;
        const parentCheckpointId = saved.parent_checkpoint_id;

        // 获取待处理的发送操作
        const pending_sends = await this._getPendingSends(
          thread_id,
          checkpoint_ns,
          parentCheckpointId
        );

        // 将对象形式的字节数组转换为 Uint8Array
        const checkpointBytes = this.objectToUint8Array(saved.checkpoint);

        // 反序列化检查点数据
        const deserializedCheckpoint = {
          ...(await this.serde.loadsTyped("json", checkpointBytes)),
          pending_sends,
        };

        // 获取写入操作
        const writesResponse = await this.apiClient.get("/writes", {
          params: {
            thread_id,
            checkpoint_ns,
            checkpoint_id: saved.checkpoint_id,
          },
        });

        const pendingWrites = await Promise.all(
          writesResponse.data.map(async (write: any) => {
            return [
              write.task_id,
              write.channel,
              await this.serde.loadsTyped(
                "json",
                this.objectToUint8Array(write.value)
              ),
            ];
          })
        );

        const checkpointTuple: CheckpointTuple = {
          config: {
            configurable: {
              thread_id,
              checkpoint_ns,
              checkpoint_id: saved.checkpoint_id,
            },
          },
          checkpoint: deserializedCheckpoint,
          metadata: await this.serde.loadsTyped(
            "json",
            this.objectToUint8Array(saved.metadata)
          ),
          pendingWrites,
        };

        if (parentCheckpointId !== undefined) {
          checkpointTuple.parentConfig = {
            configurable: {
              thread_id,
              checkpoint_ns,
              checkpoint_id: parentCheckpointId,
            },
          };
        }

        return checkpointTuple;
      }
    } catch (error: any) {
      if (error.response?.status === 404) {
        console.log(`Checkpoint not found for thread ${thread_id}`);
        return undefined;
      }
      console.error("Error retrieving checkpoint:", error);
      throw error;
    }

    return undefined;
  }

  /**
   * 列出检查点
   */
  async *list(
    config: any,
    options?: {
      before?: { configurable?: { checkpoint_id?: string } };
      limit?: number;
      filter?: Record<string, any>;
    }
  ): AsyncGenerator<any> {
    const { before, limit } = options ?? {};

    const thread_id = config.configurable?.thread_id;
    const checkpoint_ns = config.configurable?.checkpoint_ns ?? "";

    if (!thread_id) {
      console.warn("No thread_id provided in config for list operation");
      return;
    }

    try {
      const params: any = {
        thread_id,
        checkpoint_ns,
        limit: limit || 50,
        offset: 0,
      };

      // 注意：当前API不支持before参数，这里可能需要在客户端过滤
      if (before?.configurable?.checkpoint_id) {
        // 可以在获取数据后进行过滤
        console.warn("Before filtering will be applied client-side");
      }

      const response = await this.apiClient.get("/list", { params });

      if (response.status === 200 && response.data.checkpoints) {
        for (const item of response.data.checkpoints) {
          // 如果有before条件，进行客户端过滤
          if (
            before?.configurable?.checkpoint_id &&
            item.checkpoint_id >= before.configurable.checkpoint_id
          ) {
            continue;
          }

          const checkpointNs = item.checkpoint_ns || "";
          const checkpointId = item.checkpoint_id;
          const parentCheckpointId = item.parent_checkpoint_id;

          // 获取待处理的发送操作
          const pending_sends = await this._getPendingSends(
            thread_id,
            checkpointNs,
            parentCheckpointId
          );

          // 反序列化检查点数据
          const deserializedCheckpoint = {
            ...(await this.serde.loadsTyped(
              "json",
              this.objectToUint8Array(item.checkpoint)
            )),
            pending_sends,
          };

          // 获取写入操作
          const writesResponse = await this.apiClient.get("/writes", {
            params: {
              thread_id,
              checkpoint_ns: checkpointNs,
              checkpoint_id: checkpointId,
            },
          });

          const pendingWrites = await Promise.all(
            writesResponse.data.map(async (write: any) => {
              return [
                write.task_id,
                write.channel,
                await this.serde.loadsTyped(
                  "json",
                  this.objectToUint8Array(write.value)
                ),
              ];
            })
          );

          const checkpointTuple: CheckpointTuple = {
            config: {
              configurable: {
                thread_id,
                checkpoint_ns: checkpointNs,
                checkpoint_id: checkpointId,
              },
            },
            checkpoint: deserializedCheckpoint,
            metadata: await this.serde.loadsTyped(
              "json",
              this.objectToUint8Array(item.metadata)
            ),
            pendingWrites,
          };

          if (parentCheckpointId !== undefined) {
            checkpointTuple.parentConfig = {
              configurable: {
                thread_id,
                checkpoint_ns: checkpointNs,
                checkpoint_id: parentCheckpointId,
              },
            };
          }

          yield checkpointTuple;
        }
      }
    } catch (error) {
      console.error(
        `Error listing checkpoints for thread ${thread_id}:`,
        error
      );
      throw error;
    }
  }

  /**
   * 保存检查点
   */
  async put(config: any, checkpoint: any, metadata: any) {
    const preparedCheckpoint = copyCheckpoint(checkpoint);
    delete (preparedCheckpoint as any).pending_sends;

    const threadId = config.configurable?.thread_id;
    const checkpointNamespace = config.configurable?.checkpoint_ns ?? "";

    if (threadId === undefined) {
      throw new Error(
        `Failed to put checkpoint. The passed RunnableConfig is missing a required "thread_id" field in its "configurable" property.`
      );
    }

    const [, serializedCheckpoint] = this.serde.dumpsTyped(preparedCheckpoint);
    const [, serializedMetadata] = this.serde.dumpsTyped(metadata);

    try {
      const requestData = {
        thread_id: threadId,
        checkpoint_ns: checkpointNamespace,
        checkpoint_id: checkpoint.id,
        checkpoint: serializedCheckpoint,
        metadata: serializedMetadata,
        parent_checkpoint_id: config.configurable?.checkpoint_id || null,
      };

      await this.apiClient.post("", requestData);

      console.log(
        `Checkpoint saved: ${threadId}/${checkpointNamespace}/${checkpoint.id}`
      );

      return {
        configurable: {
          thread_id: threadId,
          checkpoint_ns: checkpointNamespace,
          checkpoint_id: checkpoint.id,
        },
      };
    } catch (error) {
      console.error("Error saving checkpoint:", error);
      throw error;
    }
  }

  /**
   * 保存写入操作
   */
  async putWrites(config: any, writes: any[], taskId: string) {
    const threadId = config.configurable?.thread_id;
    const checkpointNamespace = config.configurable?.checkpoint_ns ?? "";
    const checkpointId = config.configurable?.checkpoint_id;

    if (threadId === undefined) {
      throw new Error(
        `Failed to put writes. The passed RunnableConfig is missing a required "thread_id" field in its "configurable" property`
      );
    }

    if (checkpointId === undefined) {
      throw new Error(
        `Failed to put writes. The passed RunnableConfig is missing a required "checkpoint_id" field in its "configurable" property.`
      );
    }

    for (const [idx, [channel, value]] of writes.entries()) {
      const [, serializedValue] = this.serde.dumpsTyped(value);

      try {
        const requestData = {
          thread_id: threadId,
          checkpoint_ns: checkpointNamespace,
          checkpoint_id: checkpointId,
          task_id: taskId,
          channel: channel,
          value: serializedValue,
        };

        await this.apiClient.post("/writes", requestData);

        console.log(
          `Write saved: ${threadId}/${checkpointNamespace}/${checkpointId}/${taskId}/${channel}`
        );
      } catch (error) {
        console.error(`Error saving write at index ${idx}:`, error);
        throw error;
      }
    }
  }

  /**
   * 删除线程的所有检查点
   */
  async deleteThread(threadId: string, checkpointNs: string = "") {
    try {
      const params = checkpointNs ? { checkpoint_ns: checkpointNs } : {};
      await this.apiClient.delete(`/${threadId}`, { params });
      console.log(`Thread deleted: ${threadId}/${checkpointNs}`);
    } catch (error) {
      console.error(`Error deleting thread ${threadId}:`, error);
      throw error;
    }
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      const response = await this.apiClient.get("/health");
      return response.data;
    } catch (error) {
      console.error("Health check failed:", error);
      throw error;
    }
  }
}
