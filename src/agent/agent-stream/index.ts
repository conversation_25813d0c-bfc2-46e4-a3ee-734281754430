/**
 * Agent 流式输出数据结构库
 * 主入口文件
 */

// 导出核心类型定义
export {
  // 枚举类型
  MessageType,
  ToolCallStatus,
  
  // 工厂类和类型守卫
  MessageFactory,
  MessageTypeGuards
} from './types/agent-stream.js';

export type {

  // 基础接口
  BaseMessage,
  MessageData,
  ContentStructure,
  
  // 数据结构接口
  HumanMessageData,
  AgentThinkingData,
  ToolCallRequestData,
  ToolCallResultData,
  AgentResponseData,
  MessageEndData,
  
  // 工具相关接口
  ToolCall,
  ToolResult,
  ToolError,
  
  // 具体消息类型接口
  HumanMessage,
  AgentThinkingMessage,
  ToolCallRequestMessage,
  ToolCallResultMessage,
  AgentResponseMessage,
  MessageEndMessage,
  
  // 联合类型
  StreamMessage,
} from './types/agent-stream.js';

