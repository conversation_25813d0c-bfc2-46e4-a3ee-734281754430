import { promises as fs } from "fs";
import { join } from "path";

export interface TreeOptions {
  maxDepth?: number;
  maxFilesPerDir?: number;
  showHidden?: boolean;
  excludePatterns?: string[];
}

export interface TreeResult {
  output: string;
  totalDirs: number;
  totalFiles: number;
  truncated: boolean;
}

/**
 * 生成目录的树状结构
 * @param path 目录路径
 * @param options 配置选项
 * @returns 树状结构字符串和统计信息
 */
export async function generateDirectoryTree(
  path: string, 
  options: TreeOptions = {}
): Promise<TreeResult> {
  const { 
    maxDepth = 3, 
    maxFilesPerDir = 20, 
    showHidden = false,
    excludePatterns = []
  } = options;
  
  // 检查路径是否存在
  const stats = await fs.stat(path);
  if (!stats.isDirectory()) {
    throw new Error(`Path is not a directory: ${path}`);
  }
  
  const result: string[] = [];
  let totalFiles = 0;
  let totalDirs = 0;
  let truncated = false;
  
  // 检查文件/目录是否应该被排除
  function shouldExclude(name: string): boolean {
    if (!showHidden && name.startsWith('.')) {
      return true;
    }
    
    return excludePatterns.some(pattern => {
      // 简单的通配符匹配
      const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
      return regex.test(name);
    });
  }
  
  // 递归构建树状结构
  async function buildTree(currentPath: string, prefix: string = "", depth: number = 0): Promise<void> {
    if (depth >= maxDepth) {
      return;
    }
    
    try {
      const entries = await fs.readdir(currentPath, { withFileTypes: true });
      
      // 过滤文件
      const filteredEntries = entries.filter(entry => !shouldExclude(entry.name));
      
      // 分离文件夹和文件，并排序
      const dirs = filteredEntries
        .filter(entry => entry.isDirectory())
        .sort((a, b) => a.name.localeCompare(b.name));
      const files = filteredEntries
        .filter(entry => !entry.isDirectory())
        .sort((a, b) => a.name.localeCompare(b.name));
      
      // 限制每个目录下的文件数量
      const limitedDirs = dirs.slice(0, maxFilesPerDir);
      const limitedFiles = files.slice(0, maxFilesPerDir - limitedDirs.length);
      
      const allEntries = [...limitedDirs, ...limitedFiles];
      const hasMore = (dirs.length + files.length) > allEntries.length;
      
      if (hasMore) {
        truncated = true;
      }
      
      for (let i = 0; i < allEntries.length; i++) {
        const entry = allEntries[i];
        const isLast = i === allEntries.length - 1 && !hasMore;
        const connector = isLast ? "└── " : "├── ";
        const nextPrefix = prefix + (isLast ? "    " : "│   ");
        
        if (entry.isDirectory()) {
          result.push(`${prefix}${connector}${entry.name}/`);
          totalDirs++;
          await buildTree(join(currentPath, entry.name), nextPrefix, depth + 1);
        } else {
          result.push(`${prefix}${connector}${entry.name}`);
          totalFiles++;
        }
      }
      
      // 如果有更多文件被截断，显示提示
      if (hasMore) {
        const truncatedCount = (dirs.length + files.length) - allEntries.length;
        result.push(`${prefix}└── ... (${truncatedCount} more items truncated)`);
      }
      
    } catch (error: any) {
      result.push(`${prefix}└── [Error reading directory: ${error.message}]`);
    }
  }
  
  // 添加根目录
  result.push(path);
  await buildTree(path);
  
  return {
    output: result.join('\n'),
    totalDirs,
    totalFiles,
    truncated
  };
}

/**
 * 生成简化的目录树，适合用于提示词
 * @param path 目录路径
 * @param options 配置选项
 * @returns 简化的树状结构字符串
 */
export async function generateSimpleDirectoryTree(
  path: string,
  options: TreeOptions = {}
): Promise<string> {
  const defaultOptions: TreeOptions = {
    maxDepth: 2,
    maxFilesPerDir: 15,
    showHidden: false,
    excludePatterns: [
      'node_modules',
      '.git',
      'dist',
      'build',
      'out',
      '*.log',
      '.DS_Store',
      'coverage',
      '.nyc_output',
      '*.tmp',
      '*.temp'
    ],
    ...options
  };
  
  const result = await generateDirectoryTree(path, defaultOptions);
  
  // 添加简化的摘要
  const summary = `\n(${result.totalDirs} dirs, ${result.totalFiles} files${result.truncated ? ', truncated' : ''})`;
  
  return result.output + summary;
}
