import { tool } from "@langchain/core/tools";
import { interrupt } from "@langchain/langgraph";
import {
  HumanInterrupt,
  HumanInterruptConfig,
} from "@langchain/langgraph/prebuilt";

/**
 * 添加人工确认到任何工具的包装函数
 * @param toolInstance 要包装的工具实例
 * @param options 配置选项
 * @returns 包装后的工具
 */
export function addHumanInTheLoop(
  toolInstance: any,
  options: {
    interruptConfig?: HumanInterruptConfig;
    description?: string;
  } = {}
) {
  const {
    interruptConfig = {
      allow_accept: true,
      allow_edit: true,
      allow_respond: true,
      allow_ignore: false,
    },
    description = "Please review the tool call",
  } = options;

  // 创建包装工具
  const wrappedTool = tool(
    async (toolInput: any, config) => {
      // 创建中断请求
      const request: HumanInterrupt = {
        action_request: {
          action: toolInstance.name,
          args: toolInput,
        },
        config: interruptConfig,
        description: description,
      };

      // 执行中断流程
      const response = await interrupt([
        request,
        { toolCallId: config.toolCall.id },
      ]);

      // 根据响应类型处理
      switch (response.type) {
        case "accept":
          // 用户同意，执行原始工具
          return await toolInstance.invoke(toolInput, config);

        case "edit":
          // 用户编辑了参数，使用新参数执行工具
          const editedArgs = response.args?.args || toolInput;
          return await toolInstance.invoke(editedArgs, config);

        case "response":
          // 用户拒绝或提供反馈，返回用户反馈
          return response.args || "Tool call was cancelled by user";

        default:
          throw new Error(
            `Unsupported interrupt response type: ${response.type}`
          );
      }
    },
    {
      name: toolInstance.name,
      description: toolInstance.description,
      schema: toolInstance.schema,
    }
  );

  return wrappedTool;
}

/**
 * 批量为工具数组添加人工确认的函数
 * @param tools 工具数组
 * @param options 配置选项
 * @returns 包装后的工具数组
 */
export function addHumanInTheLoopToTools(
  tools: any[],
  options: {
    // 全局默认配置
    defaultInterruptConfig?: HumanInterruptConfig;
    defaultDescription?: string;
    // 针对特定工具的配置
    toolConfigs?: Record<
      string,
      {
        interruptConfig?: HumanInterruptConfig;
        description?: string;
        skip?: boolean; // 是否跳过此工具
      }
    >;
  } = {}
): any[] {
  const {
    defaultInterruptConfig = {
      allow_accept: true,
      allow_edit: true,
      allow_respond: true,
      allow_ignore: false,
    },
    defaultDescription = "Please review the tool call",
    toolConfigs = {},
  } = options;

  return tools.map((tool) => {
    const toolName = tool.name;
    const toolConfig = toolConfigs[toolName];

    // 如果配置中标记跳过此工具，则返回原工具
    if (toolConfig?.skip) {
      console.log(`Skipping human-in-the-loop for tool: ${toolName}`);
      return tool;
    }

    // 使用工具特定配置或默认配置
    const interruptConfig =
      toolConfig?.interruptConfig || defaultInterruptConfig;
    const description =
      toolConfig?.description || `${defaultDescription} (${toolName})`;

    console.log(`Adding human-in-the-loop to tool: ${toolName}`);

    return addHumanInTheLoop(tool, {
      interruptConfig,
      description,
    });
  });
}
