// import { tool } from "@langchain/core/tools";
// import { LangGraphRunnableConfig } from "@langchain/langgraph";
// import { z } from "zod";
// import { modelUsed } from "../../models.js";

// /**
//  * 物理知识机器人工具
//  * 专门解答物理相关问题的AI助手
//  */
// export const physicsRobot = tool(
//   async (input: { question: string }, config: LangGraphRunnableConfig) => {
//     console.log("Physics Robot received question:", input.question);

//     // 构建物理知识机器人的系统提示
//     const systemPrompt = `你是一个专业的物理知识机器人，专门解答物理相关的问题。你具备以下特点：

// 1. 拥有深厚的物理学知识，涵盖经典力学、热力学、电磁学、光学、量子力学、相对论等各个分支
// 2. 能够用通俗易懂的语言解释复杂的物理概念
// 3. 善于通过实例、类比和图像化描述帮助理解
// 4. 能够提供相关的公式、定律和计算方法
// 5. 对于复杂问题，会分步骤详细解答

// 请根据用户的问题，提供准确、详细且易于理解的物理知识解答。`;

//     const userMessage = `用户问题：${input.question}

// 请详细解答这个物理问题，如果涉及计算请给出具体步骤。`;

//     try {
//       // 使用流式响应
//       config.writer?.("🤖 物理机器人正在思考您的问题...\n\n");

//       const stream = await modelUsed.stream([
//         { role: "system", content: systemPrompt },
//         { role: "user", content: userMessage },
//       ]);

//       let fullResponse = "";
//       let chunkCount = 0;

//       for await (const chunk of stream) {
//         const content = chunk.content;
//         if (content && typeof content === "string") {
//           fullResponse += content;
//           chunkCount++;

//           // 每隔几个chunk输出一次，避免过于频繁的输出
//           if (chunkCount % 3 === 0 || content.includes("\n")) {
//             config.writer?.(content);
//           } else {
//             config.writer?.(content);
//           }
//         }
//       }

//       config.writer?.("\n\n✅ 物理机器人解答完成！");

//       console.log(
//         "Physics Robot response completed. Total length:",
//         fullResponse.length
//       );
//       return fullResponse;
//     } catch (error) {
//       const errorMessage = `❌ 物理机器人遇到错误: ${
//         error instanceof Error ? error.message : String(error)
//       }`;
//       console.error("Physics Robot error:", error);
//       config.writer?.(errorMessage);
//       return errorMessage;
//     }
//   },
//   {
//     name: "physics_robot",
//     schema: z.object({
//       question: z.string().describe("需要解答的物理问题"),
//     }),
//     description:
//       "这是一个物理知识机器人，当有涉及物理相关问题时，可以由该机器人解答。能够处理力学、热力学、电磁学、光学、量子力学等各个物理分支的问题，并提供详细的解释和计算步骤。",
//   }
// );
