// import { tool } from "@langchain/core/tools";
// import { LangGraphRunnableConfig } from "@langchain/langgraph";
// import { z } from "zod";

// /**
//  * 反转字符串的工具
//  */
// export const reverseString = tool(
//   async (input: { text: string }, config: LangGraphRunnableConfig) => {
//     console.log("Reversing string:", input.text);

//     config.writer?.(`reverseString begin: ${input.text}`);
//     await new Promise((resolve) => setTimeout(resolve, 1000));
//     config.writer?.(`reverseString step-1 : ${input.text}`);
//     await new Promise((resolve) => setTimeout(resolve, 1000));
//     config.writer?.(`reverseString step-2 : ${input.text}`);
//     await new Promise((resolve) => setTimeout(resolve, 1000));
//     config.writer?.(`reverseString step-3 : ${input.text}`);
//     await new Promise((resolve) => setTimeout(resolve, 1000));
//     config.writer?.(`reverseString end: ${input.text}`);

//     const result = input.text.split("").reverse().join("");
//     console.log("Reversed result:", result);
//     return result;
//   },
//   {
//     name: "reverse_string",
//     schema: z.object({
//       text: z.string().describe("The text to be reversed"),
//     }),
//     description: "Reverse the characters in a string.",
//   }
// );
