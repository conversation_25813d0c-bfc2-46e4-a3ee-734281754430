import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { promises as fs } from "fs";
import { join, relative } from "path";
import { glob } from "glob";

/**
 * 使用ripgrep风格的精确模式匹配工具
 * 在文件或目录中查找精确的模式匹配
 * 结果以JSON格式返回，包含文件名、行号和行内容
 */
export const grepSearch = tool(
  async (input: {
    searchPath: string;
    query: string;
    toolSummary?: string;
    caseInsensitive?: boolean;
    includes?: string[];
    isRegex?: boolean;
    matchPerLine?: boolean;
  }) => {
    console.log("Performing grep search:", input.query);
    console.log("Search path:", input.searchPath);
    
    try {
      // 检查搜索路径是否存在
      const stats = await fs.stat(input.searchPath);
      
      // 设置默认值
      const caseInsensitive = input.caseInsensitive || false;
      const includes = input.includes || [];
      const isRegex = input.isRegex || false;
      const matchPerLine = input.matchPerLine !== false; // 默认为true
      
      // 构建搜索模式
      let searchPattern: RegExp;
      if (isRegex) {
        const flags = caseInsensitive ? 'gi' : 'g';
        searchPattern = new RegExp(input.query, flags);
      } else {
        // 转义特殊字符用于字面量搜索
        const escapedQuery = input.query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const flags = caseInsensitive ? 'gi' : 'g';
        searchPattern = new RegExp(escapedQuery, flags);
      }
      
      let filesToSearch: string[] = [];
      
      if (stats.isFile()) {
        // 单个文件搜索
        filesToSearch = [input.searchPath];
      } else if (stats.isDirectory()) {
        // 目录搜索
        let filePattern = "**/*";
        
        // 默认排除模式
        const defaultExcludes = [
          "node_modules/**",
          ".git/**",
          "**/*.min.js",
          "**/*.map",
          ".DS_Store",
          "**/.DS_Store",
          "**/Thumbs.db",
          "**/*.tmp",
          "**/*.temp",
          "**/*.log",
          "**/coverage/**",
          "**/.nyc_output/**"
        ];
        
        // 如果有includes模式，使用它们
        if (includes.length > 0) {
          const includePatterns = includes.filter(pattern => !pattern.startsWith('!'));
          const excludePatterns = includes.filter(pattern => pattern.startsWith('!')).map(p => p.slice(1));
          
          if (includePatterns.length > 0) {
            // 搜索匹配include模式的文件
            const allFiles = [];
            for (const pattern of includePatterns) {
              const files = await glob(pattern, {
                cwd: input.searchPath,
                ignore: [...defaultExcludes, ...excludePatterns],
                nodir: true,
                absolute: false
              });
              allFiles.push(...files);
            }
            filesToSearch = [...new Set(allFiles)].map(file => join(input.searchPath, file));
          } else {
            // 只有排除模式
            const files = await glob(filePattern, {
              cwd: input.searchPath,
              ignore: [...defaultExcludes, ...excludePatterns],
              nodir: true,
              absolute: false
            });
            filesToSearch = files.map(file => join(input.searchPath, file));
          }
        } else {
          // 没有特定的include模式，搜索所有文件
          const files = await glob(filePattern, {
            cwd: input.searchPath,
            ignore: defaultExcludes,
            nodir: true,
            absolute: false
          });
          filesToSearch = files.map(file => join(input.searchPath, file));
        }
      } else {
        throw new Error(`Search path is neither a file nor a directory: ${input.searchPath}`);
      }
      
      // 搜索结果
      const results: Array<{ filename: string; lineNumber?: number; lineContent?: string }> = [];
      const maxResults = 50;
      
      for (const filePath of filesToSearch) {
        if (results.length >= maxResults) {
          break;
        }
        
        try {
          const content = await fs.readFile(filePath, 'utf8');
          // 使用绝对路径
          const absolutePath = filePath;
          
          if (matchPerLine) {
            // 返回每一行匹配
            const lines = content.split('\n');
            for (let i = 0; i < lines.length; i++) {
              if (searchPattern.test(lines[i])) {
                results.push({
                  filename: absolutePath,
                  lineNumber: i + 1,
                  lineContent: lines[i].trim()
                });
                
                if (results.length >= maxResults) {
                  break;
                }
              }
            }
          } else {
            // 只返回文件名
            if (searchPattern.test(content)) {
              results.push({
                filename: absolutePath
              });
            }
          }
        } catch (error) {
          // 跳过无法读取的文件（如二进制文件）
          continue;
        }
      }
      
      // 格式化结果
      if (results.length === 0) {
        return `No matches found for pattern: ${input.query}`;
      }
      
      if (matchPerLine) {
        const formattedResults = results.map(result => 
          `${result.filename}:${result.lineNumber}: ${result.lineContent}`
        ).join('\n');
        
        const summary = `Found ${results.length} matches${results.length >= maxResults ? ` (limited to ${maxResults})` : ''}:\n\n${formattedResults}`;
        
        console.log(`Grep search completed: ${results.length} matches found`);
        return summary;
      } else {
        const formattedResults = results.map(result => result.filename).join('\n');
        
        const summary = `Found ${results.length} files${results.length >= maxResults ? ` (limited to ${maxResults})` : ''}:\n\n${formattedResults}`;
        
        console.log(`Grep search completed: ${results.length} files found`);
        return summary;
      }
      
    } catch (error: any) {
      console.error("Error performing grep search:", error.message);
      throw new Error(`Failed to perform grep search: ${error.message}`);
    }
  },
  {
    name: "grep_search",
    schema: z.object({
      searchPath: z.string().describe("The path to search. This can be a directory or a file. This is a required parameter."),
      query: z.string().describe("The search term or pattern to look for within files."),
      toolSummary: z.string().optional().describe("Brief 2-5 word summary of what this tool is doing. Some examples: 'analyzing directory', 'searching the web', 'editing file', 'viewing file', 'running command', 'semantic searching'."),
      caseInsensitive: z.boolean().optional().describe("If true, performs a case-insensitive search."),
      includes: z.array(z.string()).optional().describe("Glob patterns to filter files found within the 'SearchPath', if 'SearchPath' is a directory. For example, '*.go' to only include Go files, or '!**/vendor/*' to exclude vendor directories. This is NOT for specifying the primary search directory; use 'SearchPath' for that. Leave empty if no glob filtering is needed or if 'SearchPath' is a single file."),
      isRegex: z.boolean().optional().describe("If true, treats Query as a regular expression pattern with special characters like *, +, (, etc. having regex meaning. If false, treats Query as a literal string where all characters are matched exactly. Use false for normal text searches and true only when you specifically need regex functionality."),
      matchPerLine: z.boolean().optional().describe("If true, returns each line that matches the query, including line numbers and snippets of matching lines (equivalent to 'git grep -nI'). If false, only returns the names of files containing the query (equivalent to 'git grep -l')."),
    }),
    description: "Use ripgrep to find exact pattern matches within files or directories. Results are returned in JSON format and for each match you will receive the: Filename, LineNumber, LineContent: the content of the matching line. Total results are capped at 50 matches. Use the Includes option to filter by file type or specific paths to refine your search.",
  }
);
