import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { promises as fs } from "fs";

/**
 * 按行号替换文件内容的工具
 * 根据指定的行号删除对应行，并替换为新内容
 */
export const replaceFileLines = tool(
  async (input: {
    targetFile: string;
    codeMarkdownLanguage: string;
    instruction: string;
    targetLines: number[];
    replacementContent: string;
    targetLintErrorIds?: string[];
    toolSummary?: string;
  }) => {
    console.log("Replacing file lines:", input.targetFile);
    console.log("Target lines:", input.targetLines);
    console.log("Instruction:", input.instruction);
    
    try {
      // 检查文件是否存在
      const stats = await fs.stat(input.targetFile);
      if (!stats.isFile()) {
        throw new Error(`Path is not a file: ${input.targetFile}`);
      }
      
      // 检查文件扩展名
      if (input.targetFile.endsWith('.ipynb')) {
        throw new Error("Cannot edit .ipynb files with this tool");
      }
      
      // 验证输入参数
      if (!input.targetLines || input.targetLines.length === 0) {
        throw new Error("targetLines must be a non-empty array");
      }
      
      // 验证行号都是正整数
      for (const lineNum of input.targetLines) {
        if (!Number.isInteger(lineNum) || lineNum < 1) {
          throw new Error(`Invalid line number: ${lineNum}. Line numbers must be positive integers starting from 1.`);
        }
      }
      
      // 读取文件内容
      const content = await fs.readFile(input.targetFile, 'utf8');
      const lines = content.split('\n');
      
      // 验证行号是否在文件范围内
      const maxLineNumber = lines.length;
      for (const lineNum of input.targetLines) {
        if (lineNum > maxLineNumber) {
          throw new Error(`Line number ${lineNum} exceeds file length (${maxLineNumber} lines)`);
        }
      }
      
      // 去重并排序行号（从大到小，这样删除时不会影响后续行号）
      const uniqueLines = [...new Set(input.targetLines)].sort((a, b) => b - a);
      
      // 记录要删除的行内容（用于日志）
      const deletedLines = uniqueLines.map(lineNum => ({
        lineNumber: lineNum,
        content: lines[lineNum - 1] // 转换为0索引
      }));
      
      // 从后往前删除指定行（避免行号偏移问题）
      let modifiedLines = [...lines];
      for (const lineNum of uniqueLines) {
        modifiedLines.splice(lineNum - 1, 1); // 转换为0索引
      }
      
      // 确定插入位置（最小行号的位置）
      const minLineNumber = Math.min(...input.targetLines);
      const insertPosition = minLineNumber - 1; // 转换为0索引
      
      // 插入替换内容
      if (input.replacementContent) {
        // 将替换内容按行分割
        const replacementLines = input.replacementContent.split('\n');
        modifiedLines.splice(insertPosition, 0, ...replacementLines);
      }
      
      // 重新组合文件内容
      const modifiedContent = modifiedLines.join('\n');
      
      // 写入修改后的内容
      await fs.writeFile(input.targetFile, modifiedContent, 'utf8');
      
      // 生成详细的操作摘要
      const deletedLinesInfo = deletedLines.map(line => 
        `  Line ${line.lineNumber}: ${line.content.trim() || '(empty line)'}`
      ).join('\n');
      
      const replacementLinesCount = input.replacementContent ? input.replacementContent.split('\n').length : 0;
      
      const summary = `File successfully modified: ${input.targetFile}\n` +
                     `Deleted ${uniqueLines.length} line(s):\n${deletedLinesInfo}\n` +
                     `Inserted ${replacementLinesCount} line(s) at position ${minLineNumber}\n` +
                     `Instruction: ${input.instruction}`;
      
      console.log(summary);
      return summary;
      
    } catch (error: any) {
      console.error("Error replacing file lines:", error.message);
      throw new Error(`Failed to replace file lines: ${error.message}`);
    }
  },
  {
    name: "replace_file_lines",
    schema: z.object({
      targetFile: z.string().describe("The target file to modify. Always specify the target file as the very first argument."),
      toolSummary: z.string().optional().describe("Brief 2-5 word summary of what this tool is doing. Some examples: 'analyzing directory', 'searching the web', 'editing file', 'viewing file', 'running command', 'semantic searching'."),
      codeMarkdownLanguage: z.string().describe("Markdown language for the code block, e.g 'python' or 'javascript'"),
      instruction: z.string().describe("A description of the changes that you are making to the file."),
      targetLines: z.array(z.number().int().min(1)).describe("Array of line numbers (1-based) to be deleted from the file. All specified lines will be removed and replaced with the replacementContent at the position of the smallest line number."),
      replacementContent: z.string().describe("The content to replace the deleted lines with. Can be empty string to just delete lines. Multi-line content will be split into separate lines."),
      targetLintErrorIds: z.array(z.string()).optional().describe("If applicable, IDs of lint errors this edit aims to fix (they'll have been given in recent IDE feedback). If you believe the edit could fix lints, do specify lint IDs; if the edit is wholly unrelated, do not. A rule of thumb is, if your edit was influenced by lint feedback, include lint IDs. Exercise honest judgement here."),
    }),
    description: "Use this tool to edit an existing file by replacing specific lines. Follow these rules:\n1. Do NOT make multiple parallel calls to this tool for the same file.\n2. Specify the exact line numbers (1-based) to be deleted in the targetLines array.\n3. All specified lines will be deleted and replaced with replacementContent at the position of the smallest line number.\n4. Line numbers must be positive integers within the file's range.\n5. Duplicate line numbers will be automatically removed.\n6. You may not edit file extensions: [.ipynb]\n7. Use this tool when you know the exact line numbers to modify, rather than searching for content patterns.",
  }
);
