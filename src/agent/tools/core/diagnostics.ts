import { tool } from "@langchain/core/tools";
import { z } from "zod";
import * as vscode from "vscode";
import * as path from "path";

/**
 * 读取文件编译问题和诊断信息的工具
 * 结合VSCode API获取文件的编译错误、警告等诊断信息
 */
export const readFileDiagnostics = tool(
  async (input: {
    filePath: string;
    includeWarnings?: boolean;
    includeHints?: boolean;
    includeInformation?: boolean;
  }) => {
    console.log("Reading file diagnostics:", input.filePath);
    
    try {
      // 将相对路径转换为绝对路径
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      const basePath = workspaceFolder ? workspaceFolder.uri.fsPath : process.cwd();
      
      const fullPath = path.isAbsolute(input.filePath) 
        ? input.filePath 
        : path.join(basePath, input.filePath);
      
      const fileUri = vscode.Uri.file(fullPath);
      
      // 检查文件是否存在
      try {
        await vscode.workspace.fs.stat(fileUri);
      } catch (error) {
        return `文件不存在: ${input.filePath}`;
      }
      
      // 获取文件的诊断信息
      const diagnostics = vscode.languages.getDiagnostics(fileUri);
      
      if (diagnostics.length === 0) {
        return `文件 ${input.filePath} 没有发现编译问题或诊断信息`;
      }
      
      // 过滤诊断信息
      const filteredDiagnostics = diagnostics.filter(diagnostic => {
        switch (diagnostic.severity) {
          case vscode.DiagnosticSeverity.Error:
            return true; // 总是包含错误
          case vscode.DiagnosticSeverity.Warning:
            return input.includeWarnings !== false; // 默认包含警告
          case vscode.DiagnosticSeverity.Information:
            return input.includeInformation === true; // 默认不包含信息
          case vscode.DiagnosticSeverity.Hint:
            return input.includeHints === true; // 默认不包含提示
          default:
            return true;
        }
      });
      
      if (filteredDiagnostics.length === 0) {
        return `文件 ${input.filePath} 在指定的严重性级别下没有发现问题`;
      }
      
      // 格式化诊断信息
      const formattedDiagnostics = filteredDiagnostics.map((diagnostic, index) => {
        const severityText = getSeverityText(diagnostic.severity);
        const range = diagnostic.range;
        const startLine = range.start.line + 1; // VSCode使用0基索引，转换为1基索引
        const startChar = range.start.character + 1;
        const endLine = range.end.line + 1;
        const endChar = range.end.character + 1;
        
        let result = `${index + 1}. [${severityText}] 第${startLine}行:${startChar}列`;
        if (startLine !== endLine || startChar !== endChar) {
          result += ` - 第${endLine}行:${endChar}列`;
        }
        result += `\n   消息: ${diagnostic.message}`;
        
        if (diagnostic.source) {
          result += `\n   来源: ${diagnostic.source}`;
        }
        
        if (diagnostic.code) {
          result += `\n   错误代码: ${diagnostic.code}`;
        }
        
        if (diagnostic.relatedInformation && diagnostic.relatedInformation.length > 0) {
          result += `\n   相关信息:`;
          diagnostic.relatedInformation.forEach((info, infoIndex) => {
            const relatedUri = info.location.uri;
            const relatedRange = info.location.range;
            const relatedLine = relatedRange.start.line + 1;
            const relatedChar = relatedRange.start.character + 1;
            result += `\n     ${infoIndex + 1}. ${path.basename(relatedUri.fsPath)}:${relatedLine}:${relatedChar} - ${info.message}`;
          });
        }
        
        return result;
      });
      
      const summary = `文件 ${input.filePath} 的诊断信息 (共${filteredDiagnostics.length}个问题):\n\n` +
                     formattedDiagnostics.join('\n\n');
      
      console.log(`File diagnostics completed: ${filteredDiagnostics.length} issues found`);
      return summary;
      
    } catch (error: any) {
      console.error("Error reading file diagnostics:", error.message);
      throw new Error(`读取文件诊断信息失败: ${error.message}`);
    }
  },
  {
    name: "read_file_diagnostics",
    schema: z.object({
      filePath: z.string().describe("要读取诊断信息的文件路径（可以是绝对路径或相对于工作区的相对路径）"),
      includeWarnings: z.boolean().optional().describe("是否包含警告信息（默认: true）"),
      includeHints: z.boolean().optional().describe("是否包含提示信息（默认: false）"),
      includeInformation: z.boolean().optional().describe("是否包含信息级别的诊断（默认: false）"),
    }),
    description: "读取指定文件的编译错误、警告和其他诊断信息。该工具使用VSCode的语言服务器提供的诊断信息，可以获取TypeScript、JavaScript、Python等语言的编译问题。",
  }
);

/**
 * 获取工作区所有文件的诊断信息汇总
 */
export const getWorkspaceDiagnostics = tool(
  async (input: {
    includeWarnings?: boolean;
    includeHints?: boolean;
    includeInformation?: boolean;
    maxFiles?: number;
  }) => {
    console.log("Getting workspace diagnostics");
    
    try {
      // 获取所有诊断信息
      const allDiagnostics = vscode.languages.getDiagnostics();
      
      if (allDiagnostics.length === 0) {
        return "工作区中没有发现任何诊断问题";
      }
      
      // 过滤和处理诊断信息
      const processedFiles: Array<{
        file: string;
        diagnostics: vscode.Diagnostic[];
      }> = [];
      
      for (const [uri, diagnostics] of allDiagnostics) {
        const filteredDiagnostics = diagnostics.filter(diagnostic => {
          switch (diagnostic.severity) {
            case vscode.DiagnosticSeverity.Error:
              return true;
            case vscode.DiagnosticSeverity.Warning:
              return input.includeWarnings !== false;
            case vscode.DiagnosticSeverity.Information:
              return input.includeInformation === true;
            case vscode.DiagnosticSeverity.Hint:
              return input.includeHints === true;
            default:
              return true;
          }
        });
        
        if (filteredDiagnostics.length > 0) {
          processedFiles.push({
            file: uri.fsPath,
            diagnostics: filteredDiagnostics
          });
        }
      }
      
      if (processedFiles.length === 0) {
        return "工作区中在指定的严重性级别下没有发现问题";
      }
      
      // 按问题数量排序，问题最多的文件排在前面
      processedFiles.sort((a, b) => b.diagnostics.length - a.diagnostics.length);
      
      // 限制文件数量
      const maxFiles = input.maxFiles || 10;
      const limitedFiles = processedFiles.slice(0, maxFiles);
      
      // 统计信息
      const totalFiles = processedFiles.length;
      const totalIssues = processedFiles.reduce((sum, file) => sum + file.diagnostics.length, 0);
      const errorCount = processedFiles.reduce((sum, file) => 
        sum + file.diagnostics.filter(d => d.severity === vscode.DiagnosticSeverity.Error).length, 0);
      const warningCount = processedFiles.reduce((sum, file) => 
        sum + file.diagnostics.filter(d => d.severity === vscode.DiagnosticSeverity.Warning).length, 0);
      
      let result = `工作区诊断信息汇总:\n`;
      result += `- 总计: ${totalFiles}个文件，${totalIssues}个问题\n`;
      result += `- 错误: ${errorCount}个，警告: ${warningCount}个\n\n`;
      
      if (limitedFiles.length < totalFiles) {
        result += `显示前${limitedFiles.length}个问题最多的文件:\n\n`;
      }
      
      // 格式化每个文件的诊断信息
      limitedFiles.forEach((fileInfo, index) => {
        const relativePath = vscode.workspace.asRelativePath(fileInfo.file);
        result += `${index + 1}. ${relativePath} (${fileInfo.diagnostics.length}个问题)\n`;
        
        fileInfo.diagnostics.forEach((diagnostic, diagIndex) => {
          const severityText = getSeverityText(diagnostic.severity);
          const line = diagnostic.range.start.line + 1;
          const char = diagnostic.range.start.character + 1;
          result += `   ${diagIndex + 1}. [${severityText}] 第${line}行:${char}列 - ${diagnostic.message}\n`;
        });
        
        result += '\n';
      });
      
      console.log(`Workspace diagnostics completed: ${totalIssues} issues in ${totalFiles} files`);
      return result;
      
    } catch (error: any) {
      console.error("Error getting workspace diagnostics:", error.message);
      throw new Error(`获取工作区诊断信息失败: ${error.message}`);
    }
  },
  {
    name: "get_workspace_diagnostics",
    schema: z.object({
      includeWarnings: z.boolean().optional().describe("是否包含警告信息（默认: true）"),
      includeHints: z.boolean().optional().describe("是否包含提示信息（默认: false）"),
      includeInformation: z.boolean().optional().describe("是否包含信息级别的诊断（默认: false）"),
      maxFiles: z.number().optional().describe("最多显示的文件数量（默认: 10）"),
    }),
    description: "获取整个工作区的诊断信息汇总，包括所有文件的编译错误和警告。结果按问题数量排序，便于快速定位问题最多的文件。",
  }
);

/**
 * 将诊断严重性转换为文本
 */
function getSeverityText(severity: vscode.DiagnosticSeverity): string {
  switch (severity) {
    case vscode.DiagnosticSeverity.Error:
      return "错误";
    case vscode.DiagnosticSeverity.Warning:
      return "警告";
    case vscode.DiagnosticSeverity.Information:
      return "信息";
    case vscode.DiagnosticSeverity.Hint:
      return "提示";
    default:
      return "未知";
  }
}
