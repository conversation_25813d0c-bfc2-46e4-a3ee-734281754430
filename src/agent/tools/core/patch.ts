import { tool } from "@langchain/core/tools";
import { promises as fs } from "fs";
import { dirname } from "path";
import { z } from "zod";

/**
 * 应用补丁编辑文本文件的工具
 * 不要用此工具编辑 Jupyter notebooks
 * 使用标准的 git diff 格式来执行文件补丁
 */
export const applyPatch = tool(
  async (input: { input: string; explanation: string }) => {
    console.log("Applying patch:", input.explanation);

    try {
      // 解析git diff补丁内容
      const patchContent = input.input;

      // 解析git diff格式的补丁
      const patches = parseGitDiff(patchContent);

      for (const patch of patches) {
        console.log(
          `Processing ${patch.operation} operation on file: ${patch.filePath}`
        );

        if (patch.operation === "create") {
          await handleCreateFile(patch.filePath, patch.content);
        } else if (patch.operation === "modify") {
          await handleModifyFile(patch.filePath, patch.hunks);
        } else if (patch.operation === "delete") {
          await handleDeleteFile(patch.filePath);
        } else {
          throw new Error(
            `Unknown operation: ${patch.operation}. Supported operations are create, modify, delete.`
          );
        }
      }

      console.log("Patch applied successfully");
      return `Patch applied successfully: ${input.explanation}`;
    } catch (error: any) {
      console.error("Error applying patch:", error.message);
      throw new Error(`Failed to apply patch: ${error.message}`);
    }
  },
  {
    name: "apply_patch",
    schema: z.object({
      input: z.string().describe("The git diff patch to apply."),
      explanation: z
        .string()
        .describe(
          "A short description of what the tool call is aiming to achieve."
        ),
    }),
    description:
      'Edit text files using standard git diff format. Do not use this tool to edit Jupyter notebooks. `apply_patch` allows you to execute a git diff/patch against text files. The input should be a standard git diff format patch. Use absolute file paths in the diff headers.\n\nExample git diff format:\n\ndiff --git a/Users/<USER>/project/file.py b/Users/<USER>/project/file.py\n--- a/Users/<USER>/project/file.py\n+++ b/Users/<USER>/project/file.py\n@@ -1,4 +1,4 @@\n def hello():\n-    print("Hello")\n+    print("Hello World")\n     return True\n\nFor new files:\ndiff --git a/Users/<USER>/project/newfile.py b/Users/<USER>/project/newfile.py\nnew file mode 100644\n--- /dev/null\n+++ b/Users/<USER>/project/newfile.py\n@@ -0,0 +1,3 @@\n+def new_function():\n+    return "new"\n\nFor deleted files:\ndiff --git a/Users/<USER>/project/oldfile.py b/Users/<USER>/project/oldfile.py\ndeleted file mode 100644\n--- a/Users/<USER>/project/oldfile.py\n+++ /dev/null\n@@ -1,3 +0,0 @@\n-def old_function():\n-    return "old"',
  }
);

// Git diff 相关类型定义
interface GitDiffHunk {
  oldStart: number;
  oldCount: number;
  newStart: number;
  newCount: number;
  lines: string[];
}

interface GitDiffPatch {
  filePath: string;
  operation: "create" | "modify" | "delete";
  content?: string;
  hunks?: GitDiffHunk[];
}

/**
 * 解析git diff格式的补丁
 */
function parseGitDiff(diffContent: string): GitDiffPatch[] {
  const patches: GitDiffPatch[] = [];
  const lines = diffContent.split("\n");
  let i = 0;

  while (i < lines.length) {
    const line = lines[i];

    if (line.startsWith("diff --git")) {
      const patch = parseSinglePatch(lines, i);
      if (patch) {
        patches.push(patch.patch);
        i = patch.nextIndex;
      } else {
        i++;
      }
    } else {
      i++;
    }
  }

  return patches;
}

/**
 * 解析单个文件的补丁
 */
function parseSinglePatch(
  lines: string[],
  startIndex: number
): { patch: GitDiffPatch; nextIndex: number } | null {
  let i = startIndex;
  const diffLine = lines[i];

  let filePath = "";

  // 尝试匹配绝对路径格式: diff --git a/absolute/path b/absolute/path
  let match = diffLine.match(/diff --git a(.+) b(.+)/);
  if (match) {
    filePath = match[2].trim();
    // 确保绝对路径以 / 开头
    if (!filePath.startsWith("/")) {
      filePath = "/" + filePath;
    }
  } else {
    return null;
  }

  i++;

  let operation: "create" | "modify" | "delete" = "modify";
  let content = "";
  const hunks: GitDiffHunk[] = [];

  // 解析文件头信息
  while (
    i < lines.length &&
    !lines[i].startsWith("@@") &&
    !lines[i].startsWith("diff --git")
  ) {
    const line = lines[i];

    if (line.startsWith("new file mode")) {
      operation = "create";
    } else if (line.startsWith("deleted file mode")) {
      operation = "delete";
    }
    i++;
  }

  // 解析hunks
  while (i < lines.length && lines[i].startsWith("@@")) {
    const hunk = parseHunk(lines, i);
    if (hunk) {
      hunks.push(hunk.hunk);
      i = hunk.nextIndex;
    } else {
      break;
    }
  }

  // 对于新文件，从hunks中提取内容
  if (operation === "create" && hunks.length > 0) {
    const contentLines: string[] = [];
    for (const hunk of hunks) {
      for (const line of hunk.lines) {
        if (line.startsWith("+")) {
          contentLines.push(line.substring(1));
        }
      }
    }
    content = contentLines.join("\n");
  }

  return {
    patch: {
      filePath,
      operation,
      content: operation === "create" ? content : undefined,
      hunks: operation === "modify" ? hunks : undefined,
    },
    nextIndex: i,
  };
}

/**
 * 解析单个hunk
 */
function parseHunk(
  lines: string[],
  startIndex: number
): { hunk: GitDiffHunk; nextIndex: number } | null {
  const hunkLine = lines[startIndex];
  const match = hunkLine.match(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/);

  if (!match) return null;

  const oldStart = parseInt(match[1]);
  const oldCount = match[2] ? parseInt(match[2]) : 1;
  const newStart = parseInt(match[3]);
  const newCount = match[4] ? parseInt(match[4]) : 1;

  let i = startIndex + 1;
  const hunkLines: string[] = [];

  // 读取hunk内容直到下一个@@或文件结束
  while (
    i < lines.length &&
    !lines[i].startsWith("@@") &&
    !lines[i].startsWith("diff --git")
  ) {
    hunkLines.push(lines[i]);
    i++;
  }

  return {
    hunk: {
      oldStart,
      oldCount,
      newStart,
      newCount,
      lines: hunkLines,
    },
    nextIndex: i,
  };
}

/**
 * 处理创建文件操作
 */
async function handleCreateFile(
  filePath: string,
  content?: string
): Promise<void> {
  if (!content) {
    throw new Error("Content is required for file creation");
  }

  // 确保目录存在
  const dir = dirname(filePath);
  await fs.mkdir(dir, { recursive: true });

  // 创建文件
  await fs.writeFile(filePath, content, "utf8");
}

/**
 * 处理修改文件操作
 */
async function handleModifyFile(
  filePath: string,
  hunks?: GitDiffHunk[]
): Promise<void> {
  if (!hunks || hunks.length === 0) {
    throw new Error("Hunks are required for file modification");
  }

  // 读取现有文件内容
  let fileContent = "";
  try {
    fileContent = await fs.readFile(filePath, "utf8");
  } catch (error) {
    throw new Error(`File not found: ${filePath}`);
  }

  let fileLines = fileContent.split("\n");

  // 按照hunk的顺序从后往前应用，避免行号偏移问题
  const sortedHunks = [...hunks].sort((a, b) => b.oldStart - a.oldStart);

  for (const hunk of sortedHunks) {
    fileLines = applyHunk(fileLines, hunk);
  }

  // 写回文件
  await fs.writeFile(filePath, fileLines.join("\n"), "utf8");
}

/**
 * 应用单个hunk到文件行
 */
function applyHunk(fileLines: string[], hunk: GitDiffHunk): string[] {
  const result = [...fileLines];
  let fileIndex = hunk.oldStart - 1; // git diff行号从1开始
  let hunkIndex = 0;

  while (hunkIndex < hunk.lines.length) {
    const line = hunk.lines[hunkIndex];

    if (line.startsWith("-")) {
      // 删除行
      if (fileIndex < result.length) {
        result.splice(fileIndex, 1);
      }
    } else if (line.startsWith("+")) {
      // 添加行
      const lineToAdd = line.substring(1);
      result.splice(fileIndex, 0, lineToAdd);
      fileIndex++;
    } else if (line.startsWith(" ") || line === "") {
      // 上下文行，跳过
      fileIndex++;
    }

    hunkIndex++;
  }

  return result;
}

/**
 * 处理删除文件操作
 */
async function handleDeleteFile(filePath: string): Promise<void> {
  try {
    await fs.unlink(filePath);
  } catch (error) {
    throw new Error(`Failed to delete file: ${filePath}`);
  }
}
