import { tool } from "@langchain/core/tools";
import { ChildProcess, spawn } from "child_process";
import { z } from "zod";
import * as vscode from "vscode";

// 存储后台进程的映射
const backgroundProcesses = new Map<string, ChildProcess>();
let terminalIdCounter = 1;

// VSCode终端和Shell集成管理
const terminals = new Map<vscode.Terminal, vscode.TerminalShellIntegration>();

vscode.window.onDidChangeTerminalShellIntegration(async ({ terminal, shellIntegration }) => {
    terminals.set(terminal, shellIntegration);
});

/**
 * 获取终端的Shell集成
 */
async function getTerminalShellIntegration(terminal: vscode.Terminal): Promise<vscode.TerminalShellIntegration | undefined> {
    let maxRetry = 10;
    let retries = 0;
    while (!terminals.has(terminal) && retries < maxRetry) {
        await new Promise(resolve => setTimeout(resolve, 100));
        retries++;
    }
    return terminals.get(terminal);
}

/**
 * 获取或创建VSCode终端
 */
function getOrCreateTerminal(cwd: string, terminalName: string = 'YiKe Agent'): vscode.Terminal {
    let terminal = vscode.window.terminals.find(t => t.name === terminalName);

    if (!terminal) {
        terminal = vscode.window.createTerminal({
            name: terminalName,
            cwd
        });
    } else {
        // 更新工作目录
        terminal.sendText(`cd "${cwd}"`);
    }

    return terminal;
}

/**
 * 在持久终端会话中执行shell命令的工具
 * 支持多行命令、目录管理、程序执行和后台进程
 */
export const runInTerminal = tool(
  async (input: {
    command: string;
    explanation: string;
    isBackground: boolean;
    cwd: string;
  }) => {
    console.log("Running command:", input.explanation);
    console.log("$ " + input.command);

    // 确定工作目录
    const workingDir = input.cwd;
    console.log("Working directory:", workingDir);

    try {
      if (input.isBackground) {
        // 后台进程 - 仍使用spawn方式，因为shellIntegration不适合长期后台进程
        const terminalId = `terminal_${terminalIdCounter++}`;

        const child = spawn("bash", ["-c", input.command], {
          stdio: ["pipe", "pipe", "pipe"],
          cwd: workingDir,
          env: process.env,
        });

        backgroundProcesses.set(terminalId, child);

        // 设置进程退出处理
        child.on("exit", (code) => {
          console.log(
            `Background process ${terminalId} exited with code ${code}`
          );
          backgroundProcesses.delete(terminalId);
        });

        console.log(
          `Background process started with terminal ID: ${terminalId}`
        );
        return `Background process started with terminal ID: ${terminalId}. Use get_terminal_output to check its status and output.`;
      } else {
        // 同步执行 - 使用VSCode终端和shellIntegration
        const result = await executeCommandWithVSCodeTerminal(input.command, workingDir);
        console.log("Command execution completed");
        return result;
      }
    } catch (error: any) {
      console.error("Error executing command:", error.message);
      throw new Error(`Failed to execute command: ${error.message}`);
    }
  },
  {
    name: "run_in_terminal",
    schema: z.object({
      command: z.string().describe("The command to run in the terminal."),
      explanation: z
        .string()
        .describe(
          "A one-sentence description of what the command does. This will be shown to the user before the command is run."
        ),
      isBackground: z
        .boolean()
        .describe(
          "Whether the command starts a background process. If true, the command will run in the background and you will not see the output. If false, the tool call will block on the command finishing, and then you will get the output. Examples of background processes: building in watch mode, starting a server. You can check the output of a background process later on by using get_terminal_output."
        ),
      cwd: z
        .string()
        .describe(
          "The current working directory where the command should be executed. This is a required parameter. Must be an absolute path or relative to the current process directory. Use this instead of 'cd' commands to change directories."
        ),
    }),
    description:
      "This tool allows you to execute shell commands in a persistent terminal session, preserving environment variables, working directory, and other context across multiple commands.\n\nCommand Execution:\n- Supports multi-line commands \n\nDirectory Management:\n- Use the cwd (current working directory) parameter to specify where commands should run\n- The cwd parameter is required and must be provided for every command\n- Must use absolute paths to avoid navigation issues\n- Do NOT use 'cd' commands; instead specify the target directory in the cwd parameter\n\nProgram Execution:\n- Supports Python, Node.js, and other executables.\n- Install dependencies via pip, npm, etc.\n\nBackground Processes:\n- For long-running tasks (e.g., servers), set isBackground=true.\n- Returns a terminal ID for checking status and runtime later.\n\nOutput Management:\n- Output is automatically truncated if longer than 60KB to prevent context overflow\n- Use filters like 'head', 'tail', 'grep' to limit output size\n- For pager commands, disable paging: use 'git --no-pager' or add '| cat'\n\nBest Practices:\n- Be specific with commands to avoid excessive output\n- Use targeted queries instead of broad scans\n- Consider using 'wc -l' to count before listing many items\n- Always specify the cwd parameter instead of using 'cd' commands\n- Provide descriptive explanations for what each command does",
  }
);

/**
 * 使用VSCode终端和shellIntegration执行命令并返回结果
 */
async function executeCommandWithVSCodeTerminal(
  command: string,
  workingDirectory: string
): Promise<string> {
  try {
    console.log(`Executing command in VSCode terminal: ${command}`);
    console.log(`Working directory: ${workingDirectory}`);

    // 获取或创建终端
    const terminal = getOrCreateTerminal(workingDirectory);
    terminal.show();

    // 获取shell集成
    const shellIntegration = await getTerminalShellIntegration(terminal);

    if (!shellIntegration) {
      console.warn("Shell integration not available, falling back to spawn");
      return await executeCommandWithSpawn(command, workingDirectory);
    }

    console.log(`Executing command with shell integration: ${command}`);

    // 使用shell集成执行命令
    const execution = shellIntegration.executeCommand(command);
    const stream = execution.read();

    let stdout = "";
    for await (const data of stream) {
      stdout += data;
      console.log(">> " + data);
    }

    console.log("Command execution completed via VSCode terminal");

    // 截断输出以防止上下文溢出（60KB限制）
    const maxOutputSize = 60 * 1024; // 60KB
    const truncatedOutput =
      stdout.length > maxOutputSize
        ? stdout.substring(0, maxOutputSize) +
          "\n\n[Output truncated - exceeded 60KB limit]"
        : stdout;

    return truncatedOutput || "Command completed successfully (no output)";

  } catch (error: any) {
    console.error("Error executing command with VSCode terminal:", error.message);
    // 如果VSCode终端执行失败，回退到spawn方式
    console.log("Falling back to spawn execution");
    return await executeCommandWithSpawn(command, workingDirectory);
  }
}

/**
 * 执行命令并返回结果（回退方案，使用spawn）
 */
async function executeCommandWithSpawn(
  command: string,
  workingDirectory: string
): Promise<string> {
  return new Promise((resolve, reject) => {
    const child = spawn("bash", ["-c", command], {
      stdio: ["pipe", "pipe", "pipe"],
      cwd: workingDirectory,
      env: process.env,
    });

    let stdout = "";
    let stderr = "";

    child.stdout?.on("data", (data) => {
      stdout += data.toString();
    });

    child.stderr?.on("data", (data) => {
      stderr += data.toString();
    });

    child.on("exit", (code) => {
      const output = stdout + (stderr ? "\n--- STDERR ---\n" + stderr : "");

      // 截断输出以防止上下文溢出（60KB限制）
      const maxOutputSize = 60 * 1024; // 60KB
      const truncatedOutput =
        output.length > maxOutputSize
          ? output.substring(0, maxOutputSize) +
            "\n\n[Output truncated - exceeded 60KB limit]"
          : output;

      if (code === 0) {
        resolve(
          truncatedOutput || "Command completed successfully (no output)"
        );
      } else {
        reject(
          new Error(
            `Command failed with exit code ${code}:\n${truncatedOutput}`
          )
        );
      }
    });

    child.on("error", (error) => {
      reject(new Error(`Failed to start command: ${error.message}`));
    });
  });
}

/**
 * 获取后台进程输出的工具
 */
export const getTerminalOutput = tool(
  async (input: { terminalId: string }) => {
    console.log("Getting terminal output for:", input.terminalId);

    const process = backgroundProcesses.get(input.terminalId);
    if (!process) {
      return `No background process found with terminal ID: ${input.terminalId}`;
    }

    // 检查进程状态
    if (process.killed || process.exitCode !== null) {
      backgroundProcesses.delete(input.terminalId);
      return `Process ${input.terminalId} has terminated with exit code: ${process.exitCode}`;
    }

    return `Process ${input.terminalId} is still running. PID: ${process.pid}`;
  },
  {
    name: "get_terminal_output",
    schema: z.object({
      terminalId: z
        .string()
        .describe("The terminal ID returned from a background process."),
    }),
    description: "Get the status and output of a background terminal process.",
  }
);
