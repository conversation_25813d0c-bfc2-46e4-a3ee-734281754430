import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { promises as fs } from "fs";
import { join, extname } from "path";
import { glob } from "glob";

/**
 * 按名称搜索文件和目录的工具
 * 使用智能大小写匹配，默认忽略git忽略的文件
 * 支持glob模式匹配和多种过滤选项
 */
export const findByName = tool(
  async (input: {
    searchDirectory: string;
    toolSummary?: string;
    excludes?: string[];
    extensions?: string[];
    fullPath?: boolean;
    maxDepth?: number;
    pattern?: string;
    type?: string;
  }) => {
    console.log("Searching for files:", input.pattern || "all files");
    console.log("Search directory:", input.searchDirectory);
    
    try {
      // 检查搜索目录是否存在
      const stats = await fs.stat(input.searchDirectory);
      if (!stats.isDirectory()) {
        throw new Error(`Search path is not a directory: ${input.searchDirectory}`);
      }
      
      // 设置默认值
      const maxDepth = input.maxDepth || 10;
      const excludes = input.excludes || [];
      const extensions = input.extensions || [];
      const fullPath = input.fullPath || false;
      const type = input.type || "any";
      
      // 构建搜索模式
      let searchPattern = "**/*";
      if (input.pattern) {
        if (fullPath) {
          searchPattern = input.pattern;
        } else {
          // 只匹配文件名
          searchPattern = `**/${input.pattern}`;
        }
      }
      
      // 默认排除模式
      const defaultExcludes = [
        "node_modules/**",
        ".git/**",
        "**/*.min.js",
        "**/*.map",
        ".DS_Store",
        "**/.DS_Store",
        "**/Thumbs.db",
        "**/*.tmp",
        "**/*.temp"
      ];
      
      // 合并排除模式
      const allExcludes = [...defaultExcludes, ...excludes];
      
      // 使用glob搜索文件
      const files = await glob(searchPattern, {
        cwd: input.searchDirectory,
        ignore: allExcludes,
        nodir: type === "file" || type === "any", // nodir为true时排除目录，为false时不排除
        maxDepth: maxDepth,
        dot: false, // 不包含隐藏文件
        absolute: false
      });
      
      // 过滤结果
      let filteredFiles = files;
      
      // 按扩展名过滤
      if (extensions.length > 0) {
        filteredFiles = filteredFiles.filter(file => {
          const ext = extname(file).slice(1); // 移除前导点
          return extensions.includes(ext);
        }) as string[];
      }
      
      // 限制结果数量
      const maxResults = 50;
      const limitedFiles = filteredFiles.slice(0, maxResults);
      
      // 获取文件详细信息
      const results = [];
      for (const file of limitedFiles) {
        const fullFilePath = join(input.searchDirectory, file);
        try {
          const stats = await fs.stat(fullFilePath);
          // const relativePath = relative(process.cwd(), fullFilePath);
          
          results.push({
            path: fullFilePath,
            type: stats.isDirectory() ? "directory" : "file",
            size: stats.isFile() ? stats.size : undefined,
            modified: stats.mtime.toISOString(),
          });
        } catch (error) {
          // 跳过无法访问的文件
          continue;
        }
      }
      
      // 格式化结果
      if (results.length === 0) {
        return `No files found matching the search criteria in: ${input.searchDirectory}`;
      }
      
      const formattedResults = results.map(result => {
        let line = `${result.type === "directory" ? "📁" : "📄"} ${result.path}`;
        if (result.size !== undefined) {
          line += ` (${formatFileSize(result.size)})`;
        }
        line += ` - ${new Date(result.modified).toLocaleString()}`;
        return line;
      }).join('\n');
      
      const summary = `Found ${results.length} ${type === "any" ? "items" : type + "s"}${filteredFiles.length > maxResults ? ` (limited to ${maxResults} out of ${filteredFiles.length})` : ''}:\n\n${formattedResults}`;
      
      console.log(`Search completed: ${results.length} items found`);
      return summary;
      
    } catch (error: any) {
      console.error("Error searching files:", error.message);
      throw new Error(`Failed to search files: ${error.message}`);
    }
  },
  {
    name: "find_by_name",
    schema: z.object({
      searchDirectory: z.string().describe("The directory to search within"),
      toolSummary: z.string().optional().describe("Brief 2-5 word summary of what this tool is doing. Some examples: 'analyzing directory', 'searching the web', 'editing file', 'viewing file', 'running command', 'semantic searching'."),
      pattern: z.string().optional().describe("Pattern to search for, supports glob format"),
      excludes: z.array(z.string()).optional().describe("Exclude files/directories that match the given glob patterns"),
      extensions: z.array(z.string()).optional().describe("File extensions to include (without leading .), matching paths must match at least one of the included extensions"),
      fullPath: z.boolean().optional().describe("Whether the full absolute path must match the glob pattern, default: only filename needs to match. Take care when specifying glob patterns with this flag on, e.g when FullPath is on, pattern '*.py' will not match to the file '/foo/bar.py', but pattern '**/*.py' will match."),
      maxDepth: z.number().optional().describe("Maximum depth to search"),
      type: z.enum(["file", "directory", "any"]).optional().describe("Type filter"),
    }),
    description: "Search for files and subdirectories within a specified directory using fd-like functionality. Search uses smart case and will ignore gitignored files by default. Pattern and Excludes both use the glob format. If you are searching for Extensions, there is no need to specify both Pattern AND Extensions. To avoid overwhelming output, the results are capped at 50 matches. Use the various arguments to filter the search scope as needed. Results will include the type, size, modification time, and relative path.",
  }
);

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(unitIndex === 0 ? 0 : 1)}${units[unitIndex]}`;
}
