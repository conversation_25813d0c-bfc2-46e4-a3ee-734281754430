import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { promises as fs } from "fs";
import { join, relative } from "path";
import { glob } from "glob";

/**
 * 在工作区中进行快速文本搜索的工具
 * 支持精确字符串或正则表达式搜索
 * 建议使用正则表达式的交替（|）或字符类来一次搜索多个潜在单词
 */
export const grepSearch = tool(
  async (input: { 
    query: string; 
    isRegexp: boolean; 
    includePattern?: string; 
    maxResults?: number 
  }) => {
    console.log("Performing grep search:", input.query);
    
    try {
      const workspaceRoot = process.cwd();
      const results: Array<{ file: string; line: number; content: string }> = [];
      
      // 确定搜索模式
      let searchPattern: RegExp;
      if (input.isRegexp) {
        searchPattern = new RegExp(input.query, 'i'); // 不区分大小写
      } else {
        // 转义特殊字符用于字面量搜索
        const escapedQuery = input.query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        searchPattern = new RegExp(escapedQuery, 'i');
      }
      
      // 确定文件搜索模式
      const filePattern = input.includePattern || '**/*';
      
      // 获取匹配的文件
      const files = await glob(filePattern, {
        cwd: workspaceRoot,
        ignore: ['node_modules/**', '.git/**', '**/*.min.js', '**/*.map'],
        nodir: true
      });
      
      // 搜索每个文件
      for (const file of files) {
        const filePath = join(workspaceRoot, file);
        
        try {
          const content = await fs.readFile(filePath, 'utf8');
          const lines = content.split('\n');
          
          for (let i = 0; i < lines.length; i++) {
            if (searchPattern.test(lines[i])) {
              results.push({
                file: relative(workspaceRoot, filePath),
                line: i + 1,
                content: lines[i].trim()
              });
              
              // 检查是否达到最大结果数
              if (input.maxResults && results.length >= input.maxResults) {
                break;
              }
            }
          }
          
          if (input.maxResults && results.length >= input.maxResults) {
            break;
          }
        } catch (error) {
          // 跳过无法读取的文件（如二进制文件）
          continue;
        }
      }
      
      // 格式化结果
      if (results.length === 0) {
        return `No matches found for pattern: ${input.query}`;
      }
      
      const formattedResults = results.map(result => 
        `${result.file}:${result.line}: ${result.content}`
      ).join('\n');
      
      const summary = `Found ${results.length} matches${input.maxResults && results.length >= input.maxResults ? ` (limited to ${input.maxResults})` : ''}:\n\n${formattedResults}`;
      
      console.log(`Grep search completed: ${results.length} matches found`);
      return summary;
    } catch (error: any) {
      console.error("Error performing grep search:", error.message);
      throw new Error(`Failed to perform grep search: ${error.message}`);
    }
  },
  {
    name: "grep_search",
    schema: z.object({
      query: z.string().describe("The pattern to search for in files in the workspace. Use regex with alternation (e.g., 'word1|word2|word3') or character classes to find multiple potential words in a single search. Be sure to set the isRegexp property properly to declare whether it's a regex or plain text pattern. Is case-insensitive."),
      isRegexp: z.boolean().describe("Whether the pattern is a regex."),
      includePattern: z.string().optional().describe("Search files matching this glob pattern. Will be applied to the relative path of files within the workspace. To search recursively inside a folder, use a proper glob pattern like \"src/folder/**\". Do not use | in includePattern."),
      maxResults: z.number().optional().describe("The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults."),
    }),
    description: "Do a fast text search in the workspace. Use this tool when you want to search with an exact string or regex. If you are not sure what words will appear in the workspace, prefer using regex patterns with alternation (|) or character classes to search for multiple potential words at once instead of making separate searches. For example, use 'function|method|procedure' to look for all of those words at once. Use includePattern to search within files matching a specific pattern, or in a specific file, using a relative path. Use this tool when you want to see an overview of a particular file, instead of using read_file many times to look for code within a file.",
  }
);

/**
 * 通过 glob 模式在工作区中搜索文件的工具
 * 只返回匹配文件的路径
 */
export const fileSearch = tool(
  async (input: { query: string; maxResults?: number }) => {
    console.log("Performing file search:", input.query);
    
    try {
      const workspaceRoot = process.cwd();
      
      // 使用 glob 搜索文件
      const files = await glob(input.query, {
        cwd: workspaceRoot,
        ignore: ['node_modules/**', '.git/**'],
        nodir: true
      });
      
      // 限制结果数量
      const limitedFiles = input.maxResults ? files.slice(0, input.maxResults) : files;
      
      // 格式化结果
      if (limitedFiles.length === 0) {
        return `No files found matching pattern: ${input.query}`;
      }
      
      const formattedResults = limitedFiles.map(file => relative(workspaceRoot, join(workspaceRoot, file))).join('\n');
      
      const summary = `Found ${limitedFiles.length} files${input.maxResults && files.length > input.maxResults ? ` (limited to ${input.maxResults} out of ${files.length})` : ''}:\n\n${formattedResults}`;
      
      console.log(`File search completed: ${limitedFiles.length} files found`);
      return summary;
    } catch (error: any) {
      console.error("Error performing file search:", error.message);
      throw new Error(`Failed to perform file search: ${error.message}`);
    }
  },
  {
    name: "file_search",
    schema: z.object({
      query: z.string().describe("Search for files with names or paths matching this glob pattern."),
      maxResults: z.number().optional().describe("The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults."),
    }),
    description: "Search for files in the workspace by glob pattern. This only returns the paths of matching files. Use this tool when you know the exact filename pattern of the files you're searching for. Glob patterns match from the root of the workspace folder. Examples:\n- **/*.{js,ts} to match all js/ts files in the workspace.\n- src/** to match all files under the top-level src folder.\n- **/foo/**/*.js to match all js files under any foo folder in the workspace.",
  }
);
