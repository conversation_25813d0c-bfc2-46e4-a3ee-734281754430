// import { tool } from "@langchain/core/tools";
// import { z } from "zod";

// /**
//  * 获取当前系统时间的工具
//  */
// export const getCurrentTime = tool(
//   async () => {
//     const now = new Date();
//     const timeStr = now.toLocaleString();
//     console.log("Current time:", timeStr);
//     return timeStr;
//   },
//   {
//     name: "get_current_time",
//     description:
//       "Get the current system time. When you need to query the current time, please prioritize using this tool.",
//     schema: z.object({}),
//   }
// );
