import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { promises as fs } from "fs";

import { generateDirectoryTree } from "../utils/treeUtils.js";

/**
 * 列出目录内容的工具
 * 结果中以 / 结尾的是文件夹，否则是文件
 */
export const listDir = tool(
  async (input: { path: string }) => {
    console.log("Listing directory:", input.path);
    
    try {
      // 检查路径是否存在
      const stats = await fs.stat(input.path);
      if (!stats.isDirectory()) {
        throw new Error(`Path is not a directory: ${input.path}`);
      }
      
      // 读取目录内容
      const entries = await fs.readdir(input.path, { withFileTypes: true });
      
      // 格式化结果
      const formattedEntries = entries.map(entry => {
        if (entry.isDirectory()) {
          return entry.name + "/";
        } else {
          return entry.name;
        }
      });
      
      // 排序：文件夹在前，然后按字母顺序
      formattedEntries.sort((a, b) => {
        const aIsDir = a.endsWith("/");
        const bIsDir = b.endsWith("/");
        
        if (aIsDir && !bIsDir) return -1;
        if (!aIsDir && bIsDir) return 1;
        return a.localeCompare(b);
      });
      
      const result = formattedEntries.join("\n");
      console.log(`Directory listing completed: ${formattedEntries.length} entries found`);
      return result;
    } catch (error: any) {
      console.error("Error listing directory:", error.message);
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  },
  {
    name: "list_dir",
    schema: z.object({
      path: z.string().describe("The absolute path to the directory to list."),
    }),
    description: "List the contents of a directory. Result will have the name of the child. If the name ends in /, it's a folder, otherwise a file",
  }
);

/**
 * 读取文件内容的工具
 * 支持分块读取大文件
 */
export const readFile = tool(
  async (input: { targetFile: string; offset: number; limit: number }) => {
    console.log("Reading file:", input.targetFile);

    try {
      // 检查文件是否存在
      const stats = await fs.stat(input.targetFile);
      if (!stats.isFile()) {
        throw new Error(`Path is not a file: ${input.targetFile}`);
      }

      // 读取文件内容
      const content = await fs.readFile(input.targetFile, 'utf8');
      const lines = content.split('\n');
      
      // 处理偏移和限制
      let startLine = 0;
      let endLine = lines.length;
      
      if (input.offset) {
        startLine = Math.max(0, input.offset - 1); // 转换为0索引
      }
      
      if (input.limit && input.offset) {
        endLine = Math.min(lines.length, startLine + input.limit);
      }
      
      // 提取指定范围的行
      const selectedLines = lines.slice(startLine, endLine);
      
      // 添加行号
      // const numberedLines = selectedLines.map((line, index) => {
      //   const lineNumber = startLine + index + 1; // 转换回1索引
      //   return `${lineNumber.toString().padStart(4, ' ')}: ${line}`;
      // });
      
      const result = selectedLines.join('\n');
      
      // 如果文件被截断，添加提示信息
      let summary = `File read successfully: ${selectedLines.length} lines, total lines: ${lines.length}`;
      if (input.offset || input.limit) {
        summary += ` (lines ${startLine + 1}-${endLine})`;
      }
      if (lines.length > 200 && !input.limit) {
        summary += "\nNote: File has more than 200 lines. Consider using offset and limit parameters for large files.";
      }
      
      console.log(summary);
      return result;
    } catch (error: any) {
      console.error("Error reading file:", error.message);
      throw new Error(`Failed to read file: ${error.message}`);
    }
  },
  {
    name: "read_file",
    schema: z.object({
      targetFile: z.string().describe("The absolute path of the file to read."),
      offset: z.number().optional().describe("The 1-based line number to start reading from. Only use this if the file is too large to read at once. If not specified, the file will be read from the beginning."),
      limit: z.number().optional().describe("The maximum number of lines to read. Only use this together with `offset` if the file is too large to read at once."),
    }),
    description: "Read the contents of a file. This tool will truncate its output at 200 lines and may be called repeatedly with offset and limit parameters to read larger files in chunks.",
  }
);

/**
 * Tree命令工具 - 以树状结构显示目录内容
 * 支持深度控制和文件数量限制，避免输出过多内容
 */
export const treeDir = tool(
  async (input: { path: string; maxDepth?: number; maxFilesPerDir?: number; showHidden?: boolean; excludePatterns?: string[] }) => {
    const { path, maxDepth = 3, maxFilesPerDir = 20, showHidden = false, excludePatterns = [] } = input;

    console.log(`Tree listing directory: ${path}, maxDepth: ${maxDepth}, maxFilesPerDir: ${maxFilesPerDir}`);

    try {
      const result = await generateDirectoryTree(path, {
        maxDepth,
        maxFilesPerDir,
        showHidden,
        excludePatterns
      });

      const summary = `\n\nSummary: ${result.totalDirs} directories, ${result.totalFiles} files (max depth: ${maxDepth}, max files per dir: ${maxFilesPerDir}${result.truncated ? ', truncated' : ''})`;

      console.log(`Tree listing completed: ${result.totalDirs} directories, ${result.totalFiles} files`);
      return result.output + summary;

    } catch (error: any) {
      console.error("Error creating tree listing:", error.message);
      throw new Error(`Failed to create tree listing: ${error.message}`);
    }
  },
  {
    name: "tree_dir",
    schema: z.object({
      path: z.string().describe("The absolute path to the directory to show as a tree."),
      maxDepth: z.number().optional().describe("Maximum depth to traverse (default: 3). Use smaller values for large directories."),
      maxFilesPerDir: z.number().optional().describe("Maximum number of files/folders to show per directory (default: 20). Prevents overwhelming output."),
      showHidden: z.boolean().optional().describe("Whether to show hidden files and directories starting with '.' (default: false)."),
      excludePatterns: z.array(z.string()).optional().describe("Array of patterns to exclude (supports wildcards like 'node_modules', '*.log'). Common patterns like node_modules, .git are excluded by default."),
    }),
    description: "Display directory structure in a tree format, similar to the 'tree' command. Supports depth control, file count limits, and exclude patterns to prevent overwhelming output. Perfect for getting an overview of project structure.",
  }
);
