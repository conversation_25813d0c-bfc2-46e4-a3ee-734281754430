// import { tool } from "@langchain/core/tools";
// import { z } from "zod";

// /**
//  * 简单测试工具
//  * 接收查询参数并返回固定字符串
//  */
// export const simpleTest = tool(
//   async (input: { query: string }) => {
//     console.log("Simple test tool received query:", input.query);
//     return "success";
//   },
//   {
//     name: "simple_test",
//     schema: z.object({
//       query: z.string().describe("查询参数"),
//     }),
//     description: "简单的测试工具，接收查询参数并返回固定字符串success",
//   }
// );