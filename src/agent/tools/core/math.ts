import { tool } from "@langchain/core/tools";
import { z } from "zod";

/**
 * 两个数字相乘的工具
 */
export const multiply = tool(
  async (input: { a: number; b: number }) => {
    console.log("Multiplying numbers:", input.a, "×", input.b);

    // 模拟计算延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    const result = input.a * input.b;
    console.log("Result:", result);
    return result;
  },
  {
    name: "multiply",
    schema: z.object({
      a: z.number().describe("First operand"),
      b: z.number().describe("Second operand"),
    }),
    description: "Multiply two numbers.",
  }
);
