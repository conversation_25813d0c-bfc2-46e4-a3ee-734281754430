import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { promises as fs } from "fs";

/**
 * 替换文件内容的工具（单次替换版本）
 * 只支持单个替换操作，不支持数组形式的多个替换块
 */
export const replaceFileContent = tool(
  async (input: {
    targetFile: string;
    codeMarkdownLanguage: string;
    instruction: string;
    targetContent: string;
    replacementContent: string;
    allowMultiple?: boolean;
    targetLintErrorIds?: string[];
    toolSummary?: string;
  }) => {
    console.log("Replacing file content (single):", input.targetFile);
    console.log("Instruction:", input.instruction);
    
    try {
      // 检查文件是否存在
      const stats = await fs.stat(input.targetFile);
      if (!stats.isFile()) {
        throw new Error(`Path is not a file: ${input.targetFile}`);
      }
      
      // 检查文件扩展名
      if (input.targetFile.endsWith('.ipynb')) {
        throw new Error("Cannot edit .ipynb files with this tool");
      }
      
      // 读取文件内容
      let content = await fs.readFile(input.targetFile, 'utf8');
      
      // 验证目标内容是否存在
      const occurrences = (content.match(new RegExp(escapeRegExp(input.targetContent), 'g')) || []).length;
      
      if (occurrences === 0) {
        throw new Error(`Target content not found in file: "${input.targetContent}"`);
      }
      
      if (occurrences > 1 && !input.allowMultiple) {
        throw new Error(`Multiple occurrences of target content found, but allowMultiple is false: "${input.targetContent}"`);
      }
      
      // 执行替换操作
      let modifiedContent: string;
      let replacements: number;
      
      if (input.allowMultiple) {
        // 替换所有匹配项
        const beforeLength = content.length;
        modifiedContent = content.split(input.targetContent).join(input.replacementContent);
        const afterLength = modifiedContent.length;
        
        // 计算替换次数（粗略估算）
        replacements = Math.floor((beforeLength - afterLength + input.replacementContent.length * input.targetContent.length) / input.targetContent.length);
        replacements = Math.max(1, replacements);
      } else {
        // 只替换第一个匹配项
        const index = content.indexOf(input.targetContent);
        if (index !== -1) {
          modifiedContent = content.substring(0, index) + 
                          input.replacementContent + 
                          content.substring(index + input.targetContent.length);
          replacements = 1;
        } else {
          modifiedContent = content;
          replacements = 0;
        }
      }
      
      // 写入修改后的内容
      await fs.writeFile(input.targetFile, modifiedContent, 'utf8');
      
      const summary = `File successfully modified: ${input.targetFile}\n` +
                     `Applied ${replacements} replacement(s)\n` +
                     `Instruction: ${input.instruction}`;
      
      console.log(summary);
      return summary;
      
    } catch (error: any) {
      console.error("Error replacing file content:", error.message);
      throw new Error(`Failed to replace file content: ${error.message}`);
    }
  },
  {
    name: "replace_file_content",
    schema: z.object({
      targetFile: z.string().describe("The target file to modify. Always specify the target file as the very first argument."),
      toolSummary: z.string().optional().describe("Brief 2-5 word summary of what this tool is doing. Some examples: 'analyzing directory', 'searching the web', 'editing file', 'viewing file', 'running command', 'semantic searching'."),
      codeMarkdownLanguage: z.string().describe("Markdown language for the code block, e.g 'python' or 'javascript'"),
      instruction: z.string().describe("A description of the changes that you are making to the file."),
      targetContent: z.string().describe("The exact string to be replaced. This must be the exact character-sequence to be replaced, including whitespace. Be very careful to include any leading whitespace otherwise this will not work at all. If allowMultiple is not true, then this must be a unique substring within the file, or else it will error."),
      replacementContent: z.string().describe("The content to replace the target content with."),
      allowMultiple: z.boolean().optional().default(false).describe("If true, multiple occurrences of 'targetContent' will be replaced by 'replacementContent' if they are found. Otherwise if multiple occurrences are found, an error will be returned."),
      targetLintErrorIds: z.array(z.string()).optional().describe("If applicable, IDs of lint errors this edit aims to fix (they'll have been given in recent IDE feedback). If you believe the edit could fix lints, do specify lint IDs; if the edit is wholly unrelated, do not. A rule of thumb is, if your edit was influenced by lint feedback, include lint IDs. Exercise honest judgement here."),
    }),
    description: "Use this tool to edit an existing file. Follow these rules:\n1. Do NOT make multiple parallel calls to this tool for the same file.\n2. Each edit should be small (preferably less than 10 lines); you can call this tool multiple times, making small changes each time.\n3. Specify the exact targetContent to be replaced and the replacementContent.\n4. Set allowMultiple to true if you want to replace all occurrences of the target content.\n5. You may not edit file extensions: [.ipynb]",
  }
);

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
