import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { promises as fs } from "fs";

/**
 * 替换文件内容的工具
 * 支持多个非相邻的编辑操作，每个操作作为一个独立的替换块
 */
export const replaceFileContent = tool(
  async (input: {
    targetFile: string;
    codeMarkdownLanguage: string;
    instruction: string;
    replacementChunks: Array<{
      allowMultiple: boolean;
      replacementContent: string;
      targetContent: string;
    }>;
    targetLintErrorIds?: string[];
    toolSummary?: string;
  }) => {
    console.log("Replacing file content:", input.targetFile);
    console.log("Instruction:", input.instruction);
    
    try {
      // 检查文件是否存在
      const stats = await fs.stat(input.targetFile);
      if (!stats.isFile()) {
        throw new Error(`Path is not a file: ${input.targetFile}`);
      }
      
      // 检查文件扩展名
      if (input.targetFile.endsWith('.ipynb')) {
        throw new Error("Cannot edit .ipynb files with this tool");
      }
      
      // 读取文件内容
      let content = await fs.readFile(input.targetFile, 'utf8');
      
      // 验证所有目标内容都存在
      for (let i = 0; i < input.replacementChunks.length; i++) {
        const chunk = input.replacementChunks[i];
        const occurrences = (content.match(new RegExp(escapeRegExp(chunk.targetContent), 'g')) || []).length;
        
        if (occurrences === 0) {
          throw new Error(`Target content not found in file (chunk ${i + 1}): "${chunk.targetContent}"`);
        }
        
        if (occurrences > 1 && !chunk.allowMultiple) {
          throw new Error(`Multiple occurrences of target content found (chunk ${i + 1}), but allowMultiple is false: "${chunk.targetContent}"`);
        }
      }
      
      // 执行替换操作
      let modifiedContent = content;
      let totalReplacements = 0;
      
      for (let i = 0; i < input.replacementChunks.length; i++) {
        const chunk = input.replacementChunks[i];
        
        if (chunk.allowMultiple) {
          // 替换所有匹配项
          const beforeLength = modifiedContent.length;
          modifiedContent = modifiedContent.split(chunk.targetContent).join(chunk.replacementContent);
          const afterLength = modifiedContent.length;
          
          // 计算替换次数（粗略估算）
          const replacements = Math.floor((beforeLength - afterLength + chunk.replacementContent.length * chunk.targetContent.length) / chunk.targetContent.length);
          totalReplacements += Math.max(1, replacements);
        } else {
          // 只替换第一个匹配项
          const index = modifiedContent.indexOf(chunk.targetContent);
          if (index !== -1) {
            modifiedContent = modifiedContent.substring(0, index) + 
                            chunk.replacementContent + 
                            modifiedContent.substring(index + chunk.targetContent.length);
            totalReplacements++;
          }
        }
      }
      
      // 写入修改后的内容
      await fs.writeFile(input.targetFile, modifiedContent, 'utf8');
      
      const summary = `File successfully modified: ${input.targetFile}\n` +
                     `Applied ${input.replacementChunks.length} replacement chunks with ${totalReplacements} total replacements\n` +
                     `Instruction: ${input.instruction}`;
      
      console.log(summary);
      return summary;
      
    } catch (error: any) {
      console.error("Error replacing file content:", error.message);
      throw new Error(`Failed to replace file content: ${error.message}`);
    }
  },
  {
    name: "replace_file_content",
    schema: z.object({
      targetFile: z.string().describe("The target file to modify. Always specify the target file as the very first argument."),
      toolSummary: z.string().optional().describe("Brief 2-5 word summary of what this tool is doing. Some examples: 'analyzing directory', 'searching the web', 'editing file', 'viewing file', 'running command', 'semantic searching'."),
      codeMarkdownLanguage: z.string().describe("Markdown language for the code block, e.g 'python' or 'javascript'"),
      instruction: z.string().describe("A description of the changes that you are making to the file."),
      replacementChunks: z.array(z.object({
        targetContent: z.string().describe("The exact string to be replaced. This must be the exact character-sequence to be replaced, including whitespace. Be very careful to include any leading whitespace otherwise this will not work at all. If AllowMultiple is not true, then this must be a unique substring within the file, or else it will error."),
        replacementContent: z.string().describe("The content to replace the target content with."),
        allowMultiple: z.boolean().describe("If true, multiple occurrences of 'targetContent' will be replaced by 'replacementContent' if they are found. Otherwise if multiple occurences are found, an error will be returned."),
      })).describe("A list of chunks to replace. It is best to provide multiple chunks for non-contiguous edits if possible. This must be a JSON array, not a string."),
      targetLintErrorIds: z.array(z.string()).optional().describe("If applicable, IDs of lint errors this edit aims to fix (they'll have been given in recent IDE feedback). If you believe the edit could fix lints, do specify lint IDs; if the edit is wholly unrelated, do not. A rule of thumb is, if your edit was influenced by lint feedback, include lint IDs. Exercise honest judgement here."),
    }),
    description: "Use this tool to edit an existing file. Follow these rules:\n1. Do NOT make multiple parallel calls to this tool for the same file.\n2. To edit multiple, non-adjacent lines of code in the same file, make a single call to this tool. Specify each edit as a separate ReplacementChunk.\n3. For each ReplacementChunk, specify TargetContent and ReplacementContent. In TargetContent, specify the precise lines of code to edit. These lines MUST EXACTLY MATCH text in the existing file content. In ReplacementContent, specify the replacement content for the specified target content. This must be a complete drop-in replacement of the TargetContent, with necessary modifications made.\n4. If you are making multiple edits across a single file, specify multiple separate ReplacementChunks. DO NOT try to replace the entire existing content with the new content, this is very expensive.\n5. You may not edit file extensions: [.ipynb]",
  }
);

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
