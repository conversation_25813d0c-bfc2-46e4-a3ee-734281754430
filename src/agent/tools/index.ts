
// 工具包装器和实用函数
export {
  addHumanInTheLoop,
  addHumanInTheLoopToTools,
} from "./utils/wrapper.js";

// 导入工具以便在分类导出中使用
import { getTerminalOutput, runInTerminal } from "./core/command.js";
import { createDirectory, createFile } from "./core/file.js";
import { findByName } from "./core/find.js";
import { listDir, readFile, treeDir } from "./core/filesystem.js";
import { grepSearch } from "./core/grep.js";
import { applyPatch } from "./core/patch.js";
// import { replaceFileContent } from "./core/replace.js";
import { replaceFileLines } from "./core/replaceLines.js";
import { fileSearch, grepSearch as oldGrepSearch } from "./core/search.js";
import { replaceFileContent } from "./core/replaceSingle.js";
import { readFileDiagnostics, getWorkspaceDiagnostics } from "./core/diagnostics.js";
// import { replaceFileContent } from "./core/replace.js";


// 所有工具的数组导出
export const allTools = [
  runInTerminal,
  getTerminalOutput,
  createFile,
  createDirectory,
  findByName,
  grepSearch,
  listDir,
  readFile,
  treeDir,
  // applyPatch,
  // replaceFileLines,
  replaceFileContent,
  // replaceFileContent,
  // oldGrepSearch,
  // fileSearch,
  readFileDiagnostics,
  getWorkspaceDiagnostics,
];
