import 'dotenv/config';
import { Chat<PERSON>lla<PERSON> } from "@langchain/ollama";
import { ChatAlibabaTongyi } from "@langchain/community/chat_models/alibaba_tongyi";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { ChatBaiduQianfan } from "@langchain/baidu-qianfan";
import { ChatOpenAI } from '@langchain/openai';


// Initialize the Ollama model
const modelOllama = new ChatOllama({
    baseUrl: "http://localhost:11434", // Default Ollama server URL
    model: "qwen2.5", // Using the qwen2.5 model that's available
    temperature: 0.7,
});

// // Initialize the Alibaba Tongyi model
// const modelTongyi = new ChatAlibabaTongyi({
//     modelName: "qwen-turbo", // or other available models like "qwen-plus"
//     temperature: 0.7,
//     alibabaApiKey: process.env.DASHSCOPE_API_KEY, // Make sure to set this in your .env file
// });

// Initialize Google Gemini model
// const modelGemini = new ChatGoogleGenerativeAI({
//     model: "gemini-1.5-pro",  // or "gemini-1.5-pro" if you have access
//     maxOutputTokens: 2048,
//     apiKey: process.env.GOOGLE_API_KEY, // Make sure to set this in your .env file
//     temperature: 0.7,
// });

// const modelQianfan = new ChatBaiduQianfan({
//   model: "ERNIE-Bot", // 或其他支持的模型
//   streaming: true,
//   temperature: 0.7,
//   qianfanAK: process.env.qianfanAK,
//   qianfanSK: process.env.qianfanSK,
// });

// const modelQianfanOpenAI = new ChatOpenAI({
//   configuration: {
//     baseURL: "https://qianfan.baidubce.com/v2",
//   },
//   apiKey: process.env.BAIDU_API_KEY,
//   model: "deepseek-v3",
// })

// const modelTongyi1 = new ChatOpenAI({
//   configuration: {
//     baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
//   },
//   apiKey: process.env.DASHSCOPE_API_KEY,
//   model: "qwen-plus",
// })

const modelProxy = new ChatOpenAI({
  configuration: {
    baseURL: "http://localhost:5001/v1",
  },
  apiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZDI3NmEyZGUtYWQzMi00YzUyLWFlNGEtZjFiNWVhOTkwYjA3IiwiZXhwIjoxNzU1MzEzMDI1LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.qN-tmB55xhVlqDrnqXr65SMs-6oPBlXcJQu-j2YpU8Y",
  model: "default",
})

// const modelSiliconflow = new ChatOpenAI({
//   configuration: {
//     baseURL: "https://api.siliconflow.cn/v1",
//   },
//   apiKey: process.env.SILICONFLOW_API_KEY,
//   model: "Qwen/QwQ-32B",
// })

// const modelTongyi = new ChatOpenAI({
//   configuration: {
//     baseURL: "http://************:31025",
//   },
//   apiKey: "sk-a05ff16ef8414a45a75db780ecd11d67",
//   model: "DeepSeek-V3",
// })


export const modelUsed = modelProxy;

export { modelOllama, modelProxy };
