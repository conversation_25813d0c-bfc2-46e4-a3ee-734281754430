import { promises as fs } from "fs";


export async function writeMessageLog(chunk: any, path: string = "/tmp/message.txt") {
  // 准备要写入的内容
  const timestamp = new Date().toISOString();
  const separator = "-".repeat(100);
  const chunkString = JSON.stringify(chunk);
  const logContent = `\n${timestamp}\n${separator}\n${chunkString}\n`;

  try {
    // 追加写入文件
    console.log(logContent);
    await fs.appendFile(path, logContent, "utf-8");
  } catch (error) {
    console.error("Error writing to message log:", error);
  }
}