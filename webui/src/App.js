"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
require("./App.css");
function App() {
    const [vscode, setVsCode] = (0, react_1.useState)(null);
    const [command, setCommand] = (0, react_1.useState)('');
    const [result, setResult] = (0, react_1.useState)('');
    const [isExecuting, setIsExecuting] = (0, react_1.useState)(false);
    (0, react_1.useEffect)(() => {
        // 初始化 VSCode API
        if (window.acquireVsCodeApi) {
            const vscodeApi = window.acquireVsCodeApi();
            setVsCode(vscodeApi);
            // 监听来自 VSCode 的消息
            window.addEventListener('message', (event) => {
                const message = event.data;
                switch (message.type) {
                    case 'commandResult':
                        setResult(message.content);
                        setIsExecuting(false);
                        break;
                    case 'commandError':
                        setResult(`错误: ${message.content}`);
                        setIsExecuting(false);
                        break;
                }
            });
        }
    }, []);
    const handleExecute = () => {
        if (!vscode || !command.trim())
            return;
        setIsExecuting(true);
        setResult('正在执行命令...');
        vscode.postMessage({
            type: 'executeCommand',
            command: command.trim()
        });
    };
    const handleKeyPress = (event) => {
        if (event.key === 'Enter' && event.ctrlKey) {
            event.preventDefault();
            handleExecute();
        }
    };
    const handleClear = () => {
        setCommand('');
        setResult('');
    };
    return (<div className="app">
      <div className="app-header">
        <h1>YiKe - 命令执行器</h1>
      </div>

      <div className="command-section">
        <label htmlFor="command-input">命令输入：</label>
        <textarea id="command-input" value={command} onChange={(e) => setCommand(e.target.value)} onKeyDown={handleKeyPress} placeholder="输入要执行的命令... (按 Ctrl+Enter 快速执行)" disabled={isExecuting} rows={3}/>
        <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
          <button className="execute-button" onClick={handleExecute} disabled={isExecuting || !command.trim()}>
            {isExecuting ? '执行中...' : '执行命令'}
          </button>
          <button className="execute-button" onClick={handleClear} style={{ backgroundColor: 'var(--vscode-button-secondaryBackground)' }}>
            清空
          </button>
        </div>
      </div>

      <div className="result-section">
        <label>执行结果：</label>
        <div className="result-output">
          {result ? (<pre>{result}</pre>) : (<div className="placeholder">执行结果将在这里显示</div>)}
        </div>
      </div>
    </div>);
}
exports.default = App;
//# sourceMappingURL=App.js.map