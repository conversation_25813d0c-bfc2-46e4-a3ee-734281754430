export interface CommandMessage {
    type: 'commandStart' | 'commandOutput' | 'commandError' | 'commandComplete' | 'commandTerminated' |
          'aiMessageStart' | 'aiMessageChunk' | 'aiMessageComplete' | 'aiError' |
          'aiToolCallStart' | 'aiToolCallComplete';
    processId?: string;
    content?: string;
    exitCode?: number;
    messageId?: string;
    chunk?: string;
    message?: AIMessage;
    error?: string;
    toolCall?: ToolCall;
}

export interface WebviewMessage {
    type: 'executeCommand' | 'terminateCommand' | 'compareFiles' | 'sendAIMessage' | 'acceptToolCall' |
          'acceptFileChange' | 'revertFileChange' | 'compareFileWithSnapshot';
    value?: string;
    file1?: string;
    file2?: string;
    command?: string;
    message?: string;
    filePath?: string;
    snapshotId?: string;
    toolCallId?: string;
    toolCall?: ToolCall;
}

export interface TabConfig {
    id: string;
    label: string;
    component: string;
}

export interface CommandState {
    isRunning: boolean;
    output: string;
    error: string;
    currentProcessId?: string;
}

export interface ContentPart {
    type: 'text' | 'tool';
    content: string;
    toolCall?: ToolCall;
}

export interface AIMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date | string; // 支持Date对象和字符串格式
    toolCalls?: ToolCall[];
    contentParts?: ContentPart[]; // 保留内容和工具调用的顺序
}

export interface ToolCall {
    id: string;
    name: string;
    parameters: Record<string, any>;
    result?: string;
    status: 'pending' | 'success' | 'error';
}

export interface AIConversationState {
    messages: AIMessage[];
    isLoading: boolean;
    error?: string;
}