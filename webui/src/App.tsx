import { useState, useEffect } from 'react';
import { CommandExecutor } from './components/CommandExecutor';
import { FileComparer } from './components/FileComparer';
import { DebugConsole } from './components/DebugConsole';
import { AIChat } from './components/AIChat';
import type { TabConfig, WebviewMessage } from './types';
import './App.css';

// VSCode API 类型定义
declare global {
    interface Window {
        acquireVsCodeApi: () => any;
    }
}

interface VSCodeAPI {
    postMessage: (message: any) => void;
    getState: () => any;
    setState: (state: any) => void;
}

const tabs: TabConfig[] = [
    { id: 'ai-chat', label: 'AI对话', component: 'ai-chat' },
    { id: 'command', label: '命令执行', component: 'command' },
    { id: 'debug', label: '调试控制台', component: 'debug' },
    { id: 'compare', label: '文件对比', component: 'compare' },
];

function App() {
    const [activeTab, setActiveTab] = useState<string>('ai-chat');
    const [isLoaded, setIsLoaded] = useState(false);
    const [vscode, setVsCode] = useState<VSCodeAPI | null>(null);

    useEffect(() => {
        console.log('YiKe App loading...');

        // 检查是否在VSCode环境中
        if (typeof window !== 'undefined' && window.acquireVsCodeApi) {
            try {
                const vscodeApi = window.acquireVsCodeApi();
                setVsCode(vscodeApi);

                // 尝试恢复之前的状态
                const previousState = vscodeApi.getState();
                if (previousState && previousState.activeTab) {
                    console.log('YiKe App: Restoring previous state, activeTab:', previousState.activeTab);
                    setActiveTab(previousState.activeTab);
                } else {
                    console.log('YiKe App: No previous state found, using default tab');
                }

                setIsLoaded(true);
                console.log('VSCode API initialized successfully');

                // 发送就绪消息
                vscodeApi.postMessage({ type: 'ready' });
            } catch (error) {
                console.error('Failed to initialize VSCode API:', error);
                setIsLoaded(true);
            }
        } else {
            console.error('VSCode API not available');
            setIsLoaded(true);
        }
    }, []);

    // 保存状态到VSCode
    useEffect(() => {
        if (vscode && isLoaded) {
            vscode.setState({ activeTab });
            console.log('YiKe App: Saved state, activeTab:', activeTab);
        }
    }, [activeTab, vscode, isLoaded]);

    const sendMessage = (message: WebviewMessage) => {
        if (vscode) {
            vscode.postMessage(message);
        }
    };

    const renderTabContent = () => {
        if (!isLoaded) {
            return <div className="loading">正在加载...</div>;
        }

        if (!vscode) {
            return (
                <div className="error-state">
                    <h3>错误</h3>
                    <p>未在VSCode环境中运行</p>
                </div>
            );
        }

        switch (activeTab) {
            case 'command':
                return <CommandExecutor onSendMessage={sendMessage} />;
            case 'debug':
                return <DebugConsole onSendMessage={sendMessage} />;
            case 'compare':
                return <FileComparer onSendMessage={sendMessage} />;
            case 'ai-chat':
                return <AIChat onSendMessage={sendMessage} />;
            default:
                return null;
        }
    };

    if (!isLoaded) {
        return (
            <div className="app">
                <div className="loading-container">
                    <div className="loading">正在加载...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="app">
            <div className="tab-container">
                <div className="tab-buttons">
                    {tabs.map(tab => (
                        <button
                            key={tab.id}
                            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            {tab.label}
                        </button>
                    ))}
                </div>

                <div className="tab-content">
                    {renderTabContent()}
                </div>
            </div>
        </div>
    );
}

export default App;
