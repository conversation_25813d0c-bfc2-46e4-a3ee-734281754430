.app {
  padding: 0px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
}

.app-header {
  margin-bottom: 20px;
}

.app-header h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--vscode-titleBar-activeForeground);
}

.command-section {
  margin-bottom: 20px;
}

.command-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--vscode-input-foreground);
}

#command-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--vscode-font-family);
  font-size: 13px;
  resize: vertical;
  min-height: 60px;
}

#command-input:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

#command-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.execute-button {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.execute-button:hover:not(:disabled) {
  background-color: var(--vscode-button-hoverBackground);
}

.execute-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.result-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--vscode-input-foreground);
}

.result-output {
  flex: 1;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  background-color: var(--vscode-editor-background);
  padding: 12px;
  overflow: auto;
  font-family: var(--vscode-editor-font-family);
  font-size: 12px;
  line-height: 1.4;
}

.result-output pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: var(--vscode-terminal-foreground);
}

.placeholder {
  color: var(--vscode-descriptionForeground);
  font-style: italic;
}

/* Tab 样式 */
.tab-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  max-width: 100%;
}

.tab-buttons {
  display: flex;
  border-bottom: 1px solid var(--vscode-panel-border);
  margin-bottom: 20px;
}

.tab-button {
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: var(--vscode-tab-inactiveForeground);
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-button:hover {
  color: var(--vscode-tab-activeForeground);
}

.tab-button.active {
  color: var(--vscode-tab-activeForeground);
  border-bottom-color: var(--vscode-tab-activeBorder);
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
}

.compare-section {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.compare-section h3 {
  margin: 0 0 20px 0;
  color: var(--vscode-titleBar-activeForeground);
}

.compare-section label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--vscode-input-foreground);
}

.compare-section input[type="text"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--vscode-font-family);
  font-size: 13px;
}

.compare-section input[type="text"]:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

/* 滚动条样式 */
.result-output::-webkit-scrollbar {
  width: 8px;
}

.result-output::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

.result-output::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.loading {
  font-size: 14px;
  color: var(--vscode-descriptionForeground);
  text-align: center;
}

.loading::after {
  content: '';
  animation: loading 1s infinite;
}

@keyframes loading {
  0% { content: ''; }
  25% { content: '.'; }
  50% { content: '..'; }
  75% { content: '...'; }
  100% { content: ''; }
}

/* 错误状态样式 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  text-align: center;
}

.error-state h3 {
  margin: 0 0 10px 0;
  color: var(--vscode-errorForeground);
  font-size: 16px;
}

.error-state p {
  margin: 0;
  color: var(--vscode-descriptionForeground);
  font-size: 14px;
}

/* CommandExecutor 样式 */
.command-executor {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 20px;
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.command-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--vscode-font-family);
  font-size: 13px;
  resize: none;
}

.command-input:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

.command-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-group {
  display: flex;
  gap: 10px;
}

.terminate-button {
  padding: 8px 16px;
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.terminate-button:hover:not(:disabled) {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

.terminate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.output-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.output-container {
  flex: 1;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  background-color: var(--vscode-terminal-background);
  padding: 12px;
  overflow: auto;
  font-family: var(--vscode-editor-font-family);
  font-size: 12px;
  line-height: 1.4;
  min-height: 200px;
}

.output-text {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: var(--vscode-terminal-foreground);
}

.error-text {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: var(--vscode-terminal-ansiRed);
}

.empty-state {
  color: var(--vscode-descriptionForeground);
  font-style: italic;
  text-align: center;
  padding: 20px;
}

/* DebugConsole 样式 - vConsole风格 */
.debug-console {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--vscode-editor-background);
}

.console-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-titleBar-activeBackground);
}

.console-header h3 {
  margin: 0;
  color: var(--vscode-titleBar-activeForeground);
  font-size: 14px;
  font-weight: 600;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: var(--vscode-titleBar-activeForeground);
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--vscode-terminal-ansiRed);
}

.status-indicator.connected {
  background-color: var(--vscode-terminal-ansiGreen);
}

.console-tabs {
  display: flex;
  background-color: var(--vscode-tab-inactiveBackground);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.console-tabs .tab-button {
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: var(--vscode-tab-inactiveForeground);
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.console-tabs .tab-button:hover {
  background-color: var(--vscode-tab-hoverBackground);
  color: var(--vscode-tab-activeForeground);
}

.console-tabs .tab-button.active {
  background-color: var(--vscode-tab-activeBackground);
  color: var(--vscode-tab-activeForeground);
  border-bottom-color: var(--vscode-tab-activeBorder);
}

.console-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--vscode-panel-background);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.clear-button, .refresh-button {
  padding: 4px 8px;
  font-size: 11px;
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.clear-button:hover, .refresh-button:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

.log-count, .network-count {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

/* 日志面板 */
.logs-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.logs-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  font-family: var(--vscode-editor-font-family);
  font-size: 12px;
}

.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 2px 0;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.log-timestamp {
  color: var(--vscode-descriptionForeground);
  font-size: 10px;
  min-width: 60px;
}

.log-level {
  font-weight: bold;
  min-width: 50px;
  font-size: 10px;
}

.log-level-log { color: var(--vscode-terminal-foreground); }
.log-level-info { color: var(--vscode-terminal-ansiBlue); }
.log-level-warn { color: var(--vscode-terminal-ansiYellow); }
.log-level-error { color: var(--vscode-terminal-ansiRed); }

.log-source {
  color: var(--vscode-terminal-ansiMagenta);
  font-size: 10px;
  min-width: 60px;
}

.log-message {
  flex: 1;
  word-break: break-word;
  white-space: pre-wrap;
  color: var(--vscode-editor-foreground);
}

/* 网络面板 */
.network-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.network-container {
  flex: 1;
  overflow-y: auto;
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
}

.network-header {
  display: grid;
  grid-template-columns: 60px 50px 1fr 60px 60px;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--vscode-list-hoverBackground);
  border-bottom: 1px solid var(--vscode-panel-border);
  font-weight: bold;
  color: var(--vscode-list-activeSelectionForeground);
}

.network-entry {
  display: grid;
  grid-template-columns: 60px 50px 1fr 60px 60px;
  gap: 8px;
  padding: 4px 12px;
  border-bottom: 1px solid var(--vscode-panel-border);
  align-items: center;
}

.network-entry:hover {
  background-color: var(--vscode-list-hoverBackground);
}

.network-timestamp {
  color: var(--vscode-descriptionForeground);
  font-size: 10px;
}

.network-method {
  font-weight: bold;
  color: var(--vscode-terminal-ansiCyan);
}

.network-url {
  color: var(--vscode-editor-foreground);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.network-status {
  font-weight: bold;
  text-align: center;
}

.status-success { color: var(--vscode-terminal-ansiGreen); }
.status-warning { color: var(--vscode-terminal-ansiYellow); }
.status-error { color: var(--vscode-terminal-ansiRed); }
.status-pending { color: var(--vscode-descriptionForeground); }

.network-duration {
  color: var(--vscode-descriptionForeground);
  text-align: right;
}

/* 系统面板 */
.system-panel {
  padding: 12px;
  overflow-y: auto;
}

.info-group {
  margin-bottom: 20px;
}

.info-group h4 {
  margin: 0 0 10px 0;
  color: var(--vscode-titleBar-activeForeground);
  font-size: 13px;
  border-bottom: 1px solid var(--vscode-panel-border);
  padding-bottom: 4px;
}

.info-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 12px;
}

.info-item label {
  min-width: 120px;
  color: var(--vscode-descriptionForeground);
  font-weight: 500;
}

.info-item span {
  color: var(--vscode-editor-foreground);
  word-break: break-all;
}

/* 存储面板 */
.storage-panel {
  padding: 12px;
  overflow-y: auto;
}

.storage-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  margin-bottom: 8px;
}

.storage-info button {
  padding: 4px 8px;
  font-size: 11px;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.storage-info button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.empty-state {
  text-align: center;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
  padding: 40px 20px;
}

/* AI Chat 样式 */
.ai-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--vscode-editor-background);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-titleBar-activeBackground);
}

.chat-header h3 {
  margin: 0;
  color: var(--vscode-titleBar-activeForeground);
  font-size: 14px;
  font-weight: 600;
}

.chat-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-indicator {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  display: flex;
  align-items: center;
  gap: 4px;
}

.loading-indicator::after {
  content: '';
  animation: loading 1s infinite;
}

.error-indicator {
  font-size: 12px;
  color: var(--vscode-errorForeground);
  display: flex;
  align-items: center;
  gap: 4px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 5px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-chat {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.welcome-message {
  max-width: 400px;
  padding: 32px;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 8px;
  background-color: var(--vscode-input-background);
}

.welcome-message h4 {
  margin: 0 0 12px 0;
  color: var(--vscode-titleBar-activeForeground);
  font-size: 16px;
}

.welcome-message p {
  margin: 8px 0;
  color: var(--vscode-descriptionForeground);
  font-size: 13px;
  line-height: 1.5;
}

.welcome-message kbd {
  background-color: var(--vscode-keybindingLabel-background);
  color: var(--vscode-keybindingLabel-foreground);
  border: 1px solid var(--vscode-keybindingLabel-border);
  border-radius: 3px;
  padding: 2px 4px;
  font-size: 11px;
  font-family: var(--vscode-editor-font-family);
}

.message {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  border-radius: 8px;
  max-width: 80%;
}

.message.user {
  align-self: flex-start;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  opacity: 0.8;
}

.message.assistant {
  align-self: flex-start;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  max-width: 93%;
  width: 100%;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.message-role {
  font-size: 12px;
  font-weight: 600;
  color: var(--vscode-descriptionForeground);
}

.message-time {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  opacity: 0.7;
}

.message-content {
  line-height: 1.5;
}

.user-message {
  white-space: pre-wrap;
  word-break: break-word;
}

.message.user .message-content {
  color: var(--vscode-editor-foreground);
}

.message.assistant .message-content {
  color: var(--vscode-editor-foreground);
}

.message.assistant .message-content h1,
.message.assistant .message-content h2,
.message.assistant .message-content h3,
.message.assistant .message-content h4,
.message.assistant .message-content h5,
.message.assistant .message-content h6 {
  color: var(--vscode-titleBar-activeForeground);
  margin: 16px 0 8px 0;
}

.message.assistant .message-content p {
  margin: 8px 0;
}

.message.assistant .message-content ul,
.message.assistant .message-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message.assistant .message-content blockquote {
  margin: 8px 0;
  padding: 8px 12px;
  border-left: 3px solid var(--vscode-textBlockQuote-border);
  background-color: var(--vscode-textBlockQuote-background);
  color: var(--vscode-editor-foreground);
}

.message.assistant .message-content code {
  background-color: var(--vscode-textCodeBlock-background);
  color: var(--vscode-editor-foreground);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--vscode-editor-font-family);
  font-size: 12px;
}

.message.assistant .message-content pre {
  margin: 8px 0;
  border-radius: 4px;
  overflow-x: auto;
}

.tool-calls {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tool-call {
  background-color: var(--vscode-editor-background);
  border: 1px solid #4A9EFF;
  border-radius: 8px;
  padding: 4px;
  font-size: 13px;
  margin: 2px 0;
  box-shadow: 0 2px 8px rgba(74, 158, 255, 0.1);
  position: relative;
}

.tool-call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  padding-bottom: 8px;
}

.tool-call-header.clickable {
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 8px;
  margin: -8px;
  border-radius: 4px;
}

.tool-call-header.clickable:hover {
  background-color: rgba(74, 158, 255, 0.05);
}

.tool-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-icon {
  font-size: 12px;
  color: #4A9EFF;
  transition: transform 0.2s ease;
}

.tool-name {
  font-weight: 600;
  color: #4A9EFF;
  font-size: 14px;
}

.tool-status {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(74, 158, 255, 0.1);
}

.tool-status.pending {
  color: var(--vscode-terminal-ansiYellow);
  background-color: rgba(255, 193, 7, 0.1);
}

.tool-status.success {
  color: var(--vscode-terminal-ansiGreen);
  background-color: rgba(40, 167, 69, 0.1);
}

.tool-status.error {
  color: var(--vscode-terminal-ansiRed);
  background-color: rgba(220, 53, 69, 0.1);
}

.tool-parameters,
.tool-result {
  margin-top: 12px;
  margin-bottom: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(74, 158, 255, 0.1);
}

.tool-parameters strong,
.tool-result strong {
  color: var(--vscode-editor-foreground);
  font-size: 12px;
  font-weight: 600;
  display: block;
  margin-bottom: 6px;
}

.tool-content {
  position: relative;
}

.tool-content code {
  display: block;
  padding: 10px;
  background-color: var(--vscode-textCodeBlock-background);
  border: 1px solid rgba(74, 158, 255, 0.2);
  border-radius: 4px;
  overflow-x: auto;
  font-size: 11px;
  line-height: 1.4;
  color: var(--vscode-editor-foreground);
  font-family: var(--vscode-editor-font-family);
  white-space: pre-wrap;
  word-break: break-word;
}

.expand-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #4A9EFF;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 10px;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.expand-button:hover {
  opacity: 1;
}

.input-container {
  border-top: 1px solid var(--vscode-panel-border);
  padding: 16px;
  background-color: var(--vscode-panel-background);
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-input {
  flex: 1;
  min-height: 40px;
  max-height: 200px;
  padding: 10px 12px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 6px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--vscode-font-family);
  font-size: 13px;
  line-height: 1.4;
  resize: none;
  overflow-y: auto;
}

.message-input:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button-container {
  display: flex;
  justify-content: flex-end;
}

.send-button {
  width: 24px;
  height: 20px;
  padding: 0;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.send-button:hover:not(:disabled) {
  background-color: var(--vscode-button-hoverBackground);
  transform: scale(1.02);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

.send-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 当前消息样式 */
.current-message {
  border: 1px solid var(--vscode-focusBorder);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    border-color: var(--vscode-focusBorder);
  }
  50% {
    border-color: var(--vscode-button-background);
  }
  100% {
    border-color: var(--vscode-focusBorder);
  }
}

.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
  padding: 8px 0;
}

.thinking-indicator span {
  animation: thinking 1.5s infinite;
}

@keyframes thinking {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* 流式内容样式 */
.streaming-content {
  position: relative;
  display: inline-block;
  width: 100%;
}

.typing-cursor {
  display: inline-block;
  color: var(--vscode-focusBorder);
  font-weight: bold;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}
