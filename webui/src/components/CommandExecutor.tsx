import { useState, useRef, useEffect } from 'react';
import type { CommandState, WebviewMessage } from '../types';

interface CommandExecutorProps {
    onSendMessage: (message: WebviewMessage) => void;
}

export const CommandExecutor: React.FC<CommandExecutorProps> = ({ onSendMessage }) => {
    const [command, setCommand] = useState('');
    const [state, setState] = useState<CommandState>({
        isRunning: false,
        output: '',
        error: '',
    });
    
    const outputRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        const handleMessage = (event: MessageEvent) => {
            const message = event.data;
            console.log('YiKe: Received message:', message);

            switch (message.type) {
                case 'commandStart':
                    console.log('YiKe: Command started with processId:', message.processId);
                    setState(prev => ({
                        ...prev,
                        isRunning: true,
                        output: '',
                        error: '',
                        currentProcessId: message.processId
                    }));
                    break;

                case 'commandOutput':
                    console.log('YiKe: Command output received:', message.content);
                    setState(prev => ({
                        ...prev,
                        output: prev.output + message.content
                    }));
                    break;

                case 'commandError':
                    console.log('YiKe: Command error received:', message.content);
                    setState(prev => ({
                        ...prev,
                        error: prev.error + message.content
                    }));
                    break;

                case 'commandComplete':
                    console.log('YiKe: Command completed with exit code:', message.exitCode);
                    setState(prev => ({
                        ...prev,
                        isRunning: false,
                        currentProcessId: undefined
                    }));
                    break;

                case 'commandTerminated':
                    console.log('YiKe: Command terminated:', message.processId);
                    setState(prev => ({
                        ...prev,
                        isRunning: false,
                        currentProcessId: undefined
                    }));
                    break;

                default:
                    console.log('YiKe: Unknown message type:', message.type);
            }
        };

        console.log('YiKe: Setting up message listener');
        window.addEventListener('message', handleMessage);
        return () => {
            console.log('YiKe: Removing message listener');
            window.removeEventListener('message', handleMessage);
        };
    }, []);

    useEffect(() => {
        if (outputRef.current) {
            outputRef.current.scrollTop = outputRef.current.scrollHeight;
        }
    }, [state.output, state.error]);

    const handleExecute = () => {
        if (command.trim() && !state.isRunning) {
            console.log('YiKe: Executing command:', command);
            onSendMessage({ type: 'executeCommand', command: command });
        }
    };

    const handleTerminate = () => {
        if (state.isRunning) {
            console.log('YiKe: Terminating command');
            onSendMessage({ type: 'terminateCommand' });
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleExecute();
        }
    };

    return (
        <div className="command-executor">
            <div className="input-section">
                <input
                    ref={inputRef}
                    type="text"
                    value={command}
                    onChange={(e) => setCommand(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="输入要执行的命令..."
                    disabled={state.isRunning}
                    className="command-input"
                />
                <div className="button-group">
                    <button
                        onClick={handleExecute}
                        disabled={!command.trim() || state.isRunning}
                        className="execute-button"
                    >
                        {state.isRunning ? '执行中...' : '执行'}
                    </button>
                    <button
                        onClick={handleTerminate}
                        disabled={!state.isRunning}
                        className="terminate-button"
                    >
                        终止
                    </button>
                </div>
            </div>
            
            <div className="output-section">
                <div ref={outputRef} className="output-container">
                    {state.output && (
                        <pre className="output-text">{state.output}</pre>
                    )}
                    {state.error && (
                        <pre className="error-text">{state.error}</pre>
                    )}
                    {!state.output && !state.error && !state.isRunning && (
                        <div className="empty-state">等待执行命令...</div>
                    )}
                </div>
            </div>
        </div>
    );
};