import { useState } from 'react';
import type { WebviewMessage } from '../types';

interface FileComparerProps {
    onSendMessage: (message: WebviewMessage) => void;
}

export const FileComparer: React.FC<FileComparerProps> = ({ onSendMessage }) => {
    const [file1, setFile1] = useState('');
    const [file2, setFile2] = useState('');
    const [isComparing, setIsComparing] = useState(false);

    const handleCompare = () => {
        if (file1.trim() && file2.trim()) {
            setIsComparing(true);
            onSendMessage({
                type: 'compareFiles',
                file1: file1.trim(),
                file2: file2.trim()
            });
            
            // 重置状态
            setTimeout(() => setIsComparing(false), 1000);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleCompare();
        }
    };

    return (
        <div className="file-comparer">
            <div className="input-section">
                <div className="file-input-group">
                    <label>文件1路径:</label>
                    <input
                        type="text"
                        value={file1}
                        onChange={(e) => setFile1(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="输入第一个文件路径..."
                        className="file-input"
                    />
                </div>
                
                <div className="file-input-group">
                    <label>文件2路径:</label>
                    <input
                        type="text"
                        value={file2}
                        onChange={(e) => setFile2(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="输入第二个文件路径..."
                        className="file-input"
                    />
                </div>
                
                <button
                    onClick={handleCompare}
                    disabled={!file1.trim() || !file2.trim() || isComparing}
                    className="compare-button"
                >
                    {isComparing ? '正在对比...' : '对比文件'}
                </button>
            </div>
            
            <div className="info-section">
                <div className="info-card">
                    <h4>使用说明</h4>
                    <ul>
                        <li>输入两个文件的完整路径</li>
                        <li>支持相对路径和绝对路径</li>
                        <li>路径中的空格需要用引号包围</li>
                        <li>点击"对比文件"按钮查看差异</li>
                    </ul>
                </div>
                
                <div className="info-card">
                    <h4>路径示例</h4>
                    <ul>
                        <li><code>./src/main.js</code> - 相对路径</li>
                        <li><code>/Users/<USER>/project/file.txt</code> - 绝对路径</li>
                        <li><code>src/components/App.tsx</code> - 工作区路径</li>
                    </ul>
                </div>
            </div>
        </div>
    );
};