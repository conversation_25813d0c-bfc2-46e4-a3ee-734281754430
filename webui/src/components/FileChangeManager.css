.file-change-manager {
    background: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 6px;
    margin-bottom: 12px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-change-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--vscode-titleBar-activeBackground);
    border-bottom: 1px solid var(--vscode-panel-border);
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.file-change-header:hover {
    background: var(--vscode-titleBar-inactiveBackground);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-icon {
    font-size: 16px;
}

.header-title {
    font-weight: 500;
    color: var(--vscode-titleBar-activeForeground);
    font-size: 14px;
}

.file-count {
    color: var(--vscode-descriptionForeground);
    font-size: 12px;
    font-weight: normal;
}

.expand-icon {
    color: var(--vscode-titleBar-activeForeground);
    font-size: 12px;
    transition: transform 0.2s ease;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

.file-change-list {
    max-height: 300px;
    overflow-y: auto;
}

.file-change-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    border-bottom: 1px solid var(--vscode-panel-border);
    transition: background-color 0.2s ease;
}

.file-change-item:last-child {
    border-bottom: none;
}

.file-change-item:hover {
    background: var(--vscode-list-hoverBackground);
}

.file-info {
    flex: 1;
    cursor: pointer;
    min-width: 0;
}

.file-path {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
}

.file-icon {
    font-size: 14px;
    flex-shrink: 0;
}

.path-text {
    color: var(--vscode-foreground);
    font-size: 13px;
    font-family: var(--vscode-editor-font-family);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-meta {
    display: flex;
    align-items: center;
    gap: 8px;
}

.timestamp {
    color: var(--vscode-descriptionForeground);
    font-size: 11px;
}

.status {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
}

.status-modified {
    background: var(--vscode-gitDecoration-modifiedResourceForeground);
    color: var(--vscode-editor-background);
}

.status-accepted {
    background: var(--vscode-gitDecoration-addedResourceForeground);
    color: var(--vscode-editor-background);
}

.status-reverted {
    background: var(--vscode-gitDecoration-deletedResourceForeground);
    color: var(--vscode-editor-background);
}

.file-actions {
    display: flex;
    gap: 6px;
    flex-shrink: 0;
}

.action-button {
    padding: 4px 8px;
    border: 1px solid var(--vscode-button-border);
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 50px;
    text-align: center;
}

.accept-button {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.accept-button:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground);
}

.revert-button {
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.revert-button:hover:not(:disabled) {
    background: var(--vscode-button-secondaryHoverBackground);
}

.action-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 滚动条样式 */
.file-change-list::-webkit-scrollbar {
    width: 8px;
}

.file-change-list::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

.file-change-list::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-hoverBackground);
    border-radius: 4px;
}

.file-change-list::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-activeBackground);
}

/* 响应式设计 */
@media (max-width: 400px) {
    .file-change-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .file-actions {
        justify-content: flex-end;
    }
    
    .action-button {
        flex: 1;
        max-width: 80px;
    }
}
