import React, { useState, useEffect } from 'react';
import type { WebviewMessage } from '../types';
import './FileChangeManager.css';

export interface FileChange {
    filePath: string;
    snapshotId: string;
    timestamp: Date;
    status: 'modified' | 'accepted' | 'reverted';
    hasSnapshot: boolean;
}

interface FileChangeManagerProps {
    onSendMessage: (message: WebviewMessage) => void;
}

export const FileChangeManager: React.FC<FileChangeManagerProps> = ({ onSendMessage }) => {
    const [fileChanges, setFileChanges] = useState<FileChange[]>([]);
    const [isExpanded, setIsExpanded] = useState(true);

    // 处理来自后端的消息
    useEffect(() => {
        const handleMessage = (event: MessageEvent) => {
            try {
                const message = event.data;
                
                // 验证消息类型和必要字段
                if (!message || typeof message !== 'object') {
                    console.warn('Received invalid message format:', message);
                    return;
                }
                
                switch (message.type) {
                    case 'fileModified':
                        if (typeof message.filePath === 'string') {
                            handleFileModified(message.filePath, message.snapshotId || '');
                        } else {
                            console.warn('Invalid fileModified message:', message);
                        }
                        break;
                        
                    case 'fileChangeAccepted':
                        if (typeof message.filePath === 'string') {
                            handleFileChangeAccepted(message.filePath);
                        } else {
                            console.warn('Invalid fileChangeAccepted message:', message);
                        }
                        break;
                        
                    case 'fileChangeReverted':
                        if (typeof message.filePath === 'string') {
                            handleFileChangeReverted(message.filePath);
                        } else {
                            console.warn('Invalid fileChangeReverted message:', message);
                        }
                        break;
                        
                    case 'snapshotCreated':
                        if (typeof message.filePath === 'string' && typeof message.snapshotId === 'string') {
                            handleSnapshotCreated(message.filePath, message.snapshotId);
                        } else {
                            console.warn('Invalid snapshotCreated message:', message);
                        }
                        break;
                        
                    default:
                        // 忽略未知类型的消息
                        break;
                }
            } catch (error) {
                console.error('Error handling message:', error);
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, []);

    const handleFileModified = (filePath: string, snapshotId: string) => {
        setFileChanges(prev => {
            // 检查文件是否已经在列表中
            const existingIndex = prev.findIndex(change => change.filePath === filePath);
            
            if (existingIndex >= 0) {
                // 文件已存在，更新状态
                const updated = [...prev];
                updated[existingIndex] = {
                    ...updated[existingIndex],
                    status: 'modified',
                    timestamp: new Date()
                };
                return updated;
            } else {
                // 新文件，添加到列表
                const newChange: FileChange = {
                    filePath,
                    snapshotId,
                    timestamp: new Date(),
                    status: 'modified',
                    hasSnapshot: true
                };
                return [...prev, newChange];
            }
        });
    };

    const handleSnapshotCreated = (filePath: string, snapshotId: string) => {
        setFileChanges(prev => {
            const existingIndex = prev.findIndex(change => change.filePath === filePath);
            
            if (existingIndex >= 0) {
                const updated = [...prev];
                updated[existingIndex] = {
                    ...updated[existingIndex],
                    snapshotId,
                    hasSnapshot: true
                };
                return updated;
            }
            
            return prev;
        });
    };

    const handleFileChangeAccepted = (filePath: string) => {
        setFileChanges(prev => 
            prev.filter(change => change.filePath !== filePath)
        );
    };

    const handleFileChangeReverted = (filePath: string) => {
        setFileChanges(prev => 
            prev.filter(change => change.filePath !== filePath)
        );
    };

    const handleAcceptChange = (filePath: string) => {
        // 发送接受变更的消息到后端
        onSendMessage({
            type: 'acceptFileChange',
            filePath: filePath
        });
    };

    const handleRevertChange = (filePath: string, snapshotId: string) => {
        // 发送撤销变更的消息到后端
        onSendMessage({
            type: 'revertFileChange',
            filePath: filePath,
            snapshotId: snapshotId
        });
    };

    const handleFileClick = (filePath: string, snapshotId: string) => {
        if (!filePath) {
            console.warn('Cannot open file comparison: filePath is missing');
            return;
        }
        
        if (!snapshotId) {
            console.warn('Cannot open file comparison: snapshotId is missing for file:', filePath);
            return;
        }
        
        try {
            // 发送打开文件对比的消息到后端
            onSendMessage({
                type: 'compareFileWithSnapshot',
                filePath: filePath,
                snapshotId: snapshotId
            });
        } catch (error) {
            console.error('Failed to send compare file message:', error);
        }
    };

    const getRelativeFilePath = (filePath: string): string => {
        if (!filePath) return '未知文件';
        
        try {
            // 处理 Windows 路径分隔符
            const normalizedPath = filePath.replace(/\\/g, '/');
            const parts = normalizedPath.split('/');
            
            // 如果路径部分少于3个，返回完整路径
            if (parts.length <= 3) {
                return filePath;
            }
            
            // 返回最后两部分路径
            return `.../${parts.slice(-2).join('/')}`;
        } catch (error) {
            console.error('Error processing file path:', filePath, error);
            return '无效路径';
        }
    };

    const formatTimestamp = (timestamp: Date): string => {
        return new Date(timestamp).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    };

    return (
        <div className="file-change-manager">
            <div className="file-change-header" onClick={() => setIsExpanded(!isExpanded)}>
                <div className="header-content">
                    <span className="header-icon">📝</span>
                    <span className="header-title">文件变更管理器</span>
                    <span className="file-count">({fileChanges.length})</span>
                </div>
                <span className={`expand-icon ${isExpanded ? 'expanded' : ''}`}>▼</span>
            </div>
            
            {isExpanded ? (
                fileChanges.length > 0 ? (
                    <div className="file-change-list">
                        {fileChanges.map((change) => (
                            <div key={change.filePath} className="file-change-item">
                                <div 
                                    className="file-info"
                                    onClick={() => handleFileClick(change.filePath, change.snapshotId)}
                                    title={`点击查看文件对比: ${change.filePath}`}
                                >
                                    <div className="file-path">
                                        <span className="file-icon">📄</span>
                                        <span className="path-text">{getRelativeFilePath(change.filePath)}</span>
                                    </div>
                                    <div className="file-meta">
                                        <span className="timestamp">{formatTimestamp(change.timestamp)}</span>
                                        <span className={`status status-${change.status}`}>
                                            {change.status === 'modified' ? '已修改' : 
                                            change.status === 'accepted' ? '已接受' : '已撤销'}
                                        </span>
                                    </div>
                                </div>
                                
                                <div className="file-actions">
                                    <button
                                        className="action-button accept-button"
                                        onClick={() => handleAcceptChange(change.filePath)}
                                        disabled={change.status !== 'modified'}
                                        title="接受此文件的变更"
                                    >
                                        ✓ 接受
                                    </button>
                                    <button
                                        className="action-button revert-button"
                                        onClick={() => handleRevertChange(change.filePath, change.snapshotId)}
                                        disabled={change.status !== 'modified' || !change.hasSnapshot}
                                        title="撤销此文件的变更"
                                    >
                                        ↶ 撤销
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="no-changes-message">
                        没有待处理的文件变更
                    </div>
                )
            ) : null}
        </div>
    );
};
