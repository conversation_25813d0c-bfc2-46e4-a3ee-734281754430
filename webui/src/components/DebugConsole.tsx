import { useState, useRef, useEffect } from 'react';
import type { WebviewMessage } from '../types';

interface DebugConsoleProps {
    onSendMessage: (message: WebviewMessage) => void;
}

interface LogEntry {
    id: string;
    timestamp: Date;
    level: 'log' | 'info' | 'warn' | 'error';
    message: string;
    source?: string;
}

interface NetworkEntry {
    id: string;
    timestamp: Date;
    method: string;
    url: string;
    status?: number;
    duration?: number;
}

interface SystemInfo {
    userAgent: string;
    platform: string;
    language: string;
    cookieEnabled: boolean;
    onLine: boolean;
    screenResolution: string;
    windowSize: string;
}

export const DebugConsole: React.FC<DebugConsoleProps> = ({ onSendMessage: _ }) => {
    const [activeTab, setActiveTab] = useState<'logs' | 'network' | 'system' | 'storage'>('logs');
    const [logs, setLogs] = useState<LogEntry[]>([]);
    const [networks, setNetworks] = useState<NetworkEntry[]>([]);
    const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
    const [isConnected] = useState(true);
    const logsRef = useRef<HTMLDivElement>(null);

    // 添加日志条目
    const addLog = (message: string, level: LogEntry['level'] = 'log', source?: string) => {
        const logEntry: LogEntry = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            level,
            message,
            source
        };
        setLogs(prev => [...prev, logEntry].slice(-1000)); // 保持最新1000条
    };

    // 添加网络请求记录
    const addNetworkEntry = (method: string, url: string, status?: number, duration?: number) => {
        const networkEntry: NetworkEntry = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            method,
            url,
            status,
            duration
        };
        setNetworks(prev => [...prev, networkEntry].slice(-500)); // 保持最新500条
    };

    // 收集系统信息
    const collectSystemInfo = (): SystemInfo => {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screenResolution: `${screen.width}x${screen.height}`,
            windowSize: `${window.innerWidth}x${window.innerHeight}`
        };
    };

    // 清空日志
    const clearLogs = () => {
        setLogs([]);
    };

    // 清空网络记录
    const clearNetworks = () => {
        setNetworks([]);
    };

    // 拦截console方法
    const interceptConsole = () => {
        const originalLog = console.log;
        const originalInfo = console.info;
        const originalWarn = console.warn;
        const originalError = console.error;

        console.log = (...args) => {
            addLog(args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '), 'log', 'console');
            originalLog.apply(console, args);
        };

        console.info = (...args) => {
            addLog(args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '), 'info', 'console');
            originalInfo.apply(console, args);
        };

        console.warn = (...args) => {
            addLog(args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '), 'warn', 'console');
            originalWarn.apply(console, args);
        };

        console.error = (...args) => {
            addLog(args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '), 'error', 'console');
            originalError.apply(console, args);
        };

        return () => {
            console.log = originalLog;
            console.info = originalInfo;
            console.warn = originalWarn;
            console.error = originalError;
        };
    };

    // 拦截网络请求
    const interceptNetwork = () => {
        const originalFetch = window.fetch;

        window.fetch = async (...args) => {
            const startTime = Date.now();
            const url = typeof args[0] === 'string' ? args[0] : (args[0] as Request).url;
            const method = args[1]?.method || 'GET';

            try {
                const response = await originalFetch.apply(window, args);
                const duration = Date.now() - startTime;
                addNetworkEntry(method, url, response.status, duration);
                return response;
            } catch (error) {
                const duration = Date.now() - startTime;
                addNetworkEntry(method, url, 0, duration);
                throw error;
            }
        };

        return () => {
            window.fetch = originalFetch;
        };
    };

    // 初始化
    useEffect(() => {
        // 收集系统信息
        setSystemInfo(collectSystemInfo());

        // 拦截console和网络请求
        const restoreConsole = interceptConsole();
        const restoreNetwork = interceptNetwork();

        // 添加初始日志
        addLog('YiKe 调试控制台已启动', 'info', 'system');
        addLog('开始监控console输出和网络请求', 'info', 'system');

        return () => {
            restoreConsole();
            restoreNetwork();
        };
    }, []);

    // 处理来自VSCode的消息
    useEffect(() => {
        const handleMessage = (event: MessageEvent) => {
            const message = event.data;

            switch (message.type) {
                case 'commandStart':
                    addLog('命令开始执行...', 'info', 'vscode');
                    break;
                case 'commandOutput':
                    addLog(message.content, 'log', 'vscode');
                    break;
                case 'commandError':
                    addLog(message.content, 'error', 'vscode');
                    break;
                case 'commandComplete':
                    addLog(`命令执行完成 (退出码: ${message.exitCode})`, 'info', 'vscode');
                    break;
                case 'commandTerminated':
                    addLog('命令已终止', 'warn', 'vscode');
                    break;
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, []);

    // 自动滚动到底部
    useEffect(() => {
        if (logsRef.current && activeTab === 'logs') {
            logsRef.current.scrollTop = logsRef.current.scrollHeight;
        }
    }, [logs, activeTab]);

    // 渲染日志条目
    const renderLogEntry = (log: LogEntry) => {
        const levelClass = `log-level-${log.level}`;
        return (
            <div key={log.id} className={`log-entry ${levelClass}`}>
                <span className="log-timestamp">{log.timestamp.toLocaleTimeString()}</span>
                <span className="log-level">[{log.level.toUpperCase()}]</span>
                {log.source && <span className="log-source">[{log.source}]</span>}
                <span className="log-message">{log.message}</span>
            </div>
        );
    };

    // 渲染网络条目
    const renderNetworkEntry = (network: NetworkEntry) => {
        const statusClass = network.status ?
            (network.status >= 200 && network.status < 300 ? 'status-success' :
             network.status >= 400 ? 'status-error' : 'status-warning') : 'status-pending';

        return (
            <div key={network.id} className="network-entry">
                <span className="network-timestamp">{network.timestamp.toLocaleTimeString()}</span>
                <span className="network-method">{network.method}</span>
                <span className="network-url">{network.url}</span>
                <span className={`network-status ${statusClass}`}>
                    {network.status || 'Pending'}
                </span>
                <span className="network-duration">
                    {network.duration ? `${network.duration}ms` : '-'}
                </span>
            </div>
        );
    };

    return (
        <div className="debug-console">
            <div className="console-header">
                <h3>调试控制台 (vConsole风格)</h3>
                <div className="connection-status">
                    <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}></span>
                    {isConnected ? '已连接' : '未连接'}
                </div>
            </div>

            <div className="console-tabs">
                <button
                    className={`tab-button ${activeTab === 'logs' ? 'active' : ''}`}
                    onClick={() => setActiveTab('logs')}
                >
                    日志 ({logs.length})
                </button>
                <button
                    className={`tab-button ${activeTab === 'network' ? 'active' : ''}`}
                    onClick={() => setActiveTab('network')}
                >
                    网络 ({networks.length})
                </button>
                <button
                    className={`tab-button ${activeTab === 'system' ? 'active' : ''}`}
                    onClick={() => setActiveTab('system')}
                >
                    系统
                </button>
                <button
                    className={`tab-button ${activeTab === 'storage' ? 'active' : ''}`}
                    onClick={() => setActiveTab('storage')}
                >
                    存储
                </button>
            </div>

            <div className="console-content">
                {activeTab === 'logs' && (
                    <div className="logs-panel">
                        <div className="panel-toolbar">
                            <button onClick={clearLogs} className="clear-button">清空日志</button>
                            <span className="log-count">共 {logs.length} 条日志</span>
                        </div>
                        <div className="logs-container" ref={logsRef}>
                            {logs.map(renderLogEntry)}
                            {logs.length === 0 && (
                                <div className="empty-state">暂无日志记录</div>
                            )}
                        </div>
                    </div>
                )}

                {activeTab === 'network' && (
                    <div className="network-panel">
                        <div className="panel-toolbar">
                            <button onClick={clearNetworks} className="clear-button">清空记录</button>
                            <span className="network-count">共 {networks.length} 条请求</span>
                        </div>
                        <div className="network-container">
                            <div className="network-header">
                                <span>时间</span>
                                <span>方法</span>
                                <span>URL</span>
                                <span>状态</span>
                                <span>耗时</span>
                            </div>
                            {networks.map(renderNetworkEntry)}
                            {networks.length === 0 && (
                                <div className="empty-state">暂无网络请求记录</div>
                            )}
                        </div>
                    </div>
                )}

                {activeTab === 'system' && (
                    <div className="system-panel">
                        <div className="panel-toolbar">
                            <button onClick={() => setSystemInfo(collectSystemInfo())} className="refresh-button">
                                刷新信息
                            </button>
                        </div>
                        {systemInfo && (
                            <div className="system-info">
                                <div className="info-group">
                                    <h4>浏览器信息</h4>
                                    <div className="info-item">
                                        <label>用户代理:</label>
                                        <span>{systemInfo.userAgent}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>平台:</label>
                                        <span>{systemInfo.platform}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>语言:</label>
                                        <span>{systemInfo.language}</span>
                                    </div>
                                </div>

                                <div className="info-group">
                                    <h4>功能支持</h4>
                                    <div className="info-item">
                                        <label>Cookie:</label>
                                        <span>{systemInfo.cookieEnabled ? '支持' : '不支持'}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>在线状态:</label>
                                        <span>{systemInfo.onLine ? '在线' : '离线'}</span>
                                    </div>
                                </div>

                                <div className="info-group">
                                    <h4>显示信息</h4>
                                    <div className="info-item">
                                        <label>屏幕分辨率:</label>
                                        <span>{systemInfo.screenResolution}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>窗口大小:</label>
                                        <span>{systemInfo.windowSize}</span>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'storage' && (
                    <div className="storage-panel">
                        <div className="info-group">
                            <h4>LocalStorage</h4>
                            <div className="storage-info">
                                <span>项目数: {localStorage.length}</span>
                                <button onClick={() => {
                                    const items = [];
                                    for (let i = 0; i < localStorage.length; i++) {
                                        const key = localStorage.key(i);
                                        if (key) {
                                            items.push(`${key}: ${localStorage.getItem(key)}`);
                                        }
                                    }
                                    addLog(`LocalStorage内容:\n${items.join('\n')}`, 'info', 'storage');
                                    setActiveTab('logs');
                                }}>
                                    查看内容
                                </button>
                            </div>
                        </div>

                        <div className="info-group">
                            <h4>SessionStorage</h4>
                            <div className="storage-info">
                                <span>项目数: {sessionStorage.length}</span>
                                <button onClick={() => {
                                    const items = [];
                                    for (let i = 0; i < sessionStorage.length; i++) {
                                        const key = sessionStorage.key(i);
                                        if (key) {
                                            items.push(`${key}: ${sessionStorage.getItem(key)}`);
                                        }
                                    }
                                    addLog(`SessionStorage内容:\n${items.join('\n')}`, 'info', 'storage');
                                    setActiveTab('logs');
                                }}>
                                    查看内容
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
