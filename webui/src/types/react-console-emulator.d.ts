declare module 'react-console-emulator' {
  import { Component } from 'react';

  interface TerminalProps {
    commands?: Record<string, any>;
    welcomeMessage?: string;
    promptLabel?: string;
    commandCallback?: (command: string) => string | Promise<string>;
    style?: React.CSSProperties;
    contentStyle?: React.CSSProperties;
    promptLabelStyle?: React.CSSProperties;
    inputStyle?: React.CSSProperties;
  }

  class Terminal extends Component<TerminalProps> {
    pushToStdout(message: string): void;
    clearStdout(): void;
  }

  export default Terminal;
}
