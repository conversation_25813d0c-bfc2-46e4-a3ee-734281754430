{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "lib": ["ES2020"], "outDir": "./out", "rootDir": "./src", "sourceMap": true, "strict": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": false, "checkJs": false, "declaration": false, "declarationMap": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmitOnError": true, "incremental": true, "tsBuildInfoFile": "./out/.tsbuildinfo"}, "include": ["src/**/*"], "exclude": ["node_modules/**/*", "out/**/*", "webui/**/*", ".vscode-test/**/*", "**/*.test.ts", "**/*.spec.ts", "test-*.html", "test-*.md", "**/*.d.ts.map", "**/*.js.map", "*.py"]}